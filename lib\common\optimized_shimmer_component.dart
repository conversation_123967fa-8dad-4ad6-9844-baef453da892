import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/config/colors.dart';

/// مكونات shimmer محسنة للأداء
class OptimizedShimmerComponent {
  OptimizedShimmerComponent._();

  /// إعدادات Shimmer محسنة
  static Widget _buildShimmerWrapper({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
  }) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? greyE7.withOpacity(0.3),
      highlightColor: highlightColor ?? Colors.white.withOpacity(0.8),
      period: const Duration(milliseconds: 1200), // مدة أقصر للأداء
      child: child,
    );
  }

  /// مكون shimmer أساسي محسن
  static Widget shimmerBox({
    required double height,
    required double width,
    double radius = 15,
    EdgeInsets? margin,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(radius),
      ),
    );
  }

  /// Shimmer للطبقة الأفقية - نسخة محسنة
  static Widget optimizedHorizontalCategoryShimmer() {
    return _buildShimmerWrapper(
      child: Container(
        width: getSize().width * 0.7,
        margin: const EdgeInsetsDirectional.only(end: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          children: [
            // أيقونة الطبقة
            shimmerBox(height: 65, width: 65, radius: 6),
            
            const SizedBox(width: 16),
            
            // معلومات الطبقة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  shimmerBox(height: 12, width: double.infinity),
                  const SizedBox(height: 8),
                  shimmerBox(height: 10, width: 80),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shimmer للطبقة العمودية - نسخة محسنة
  static Widget optimizedCategoryItemShimmer() {
    return _buildShimmerWrapper(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 20),
        child: Row(
          children: [
            // أيقونة دائرية
            shimmerBox(height: 34, width: 34, radius: 17),
            
            const SizedBox(width: 12),
            
            // معلومات الطبقة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  shimmerBox(height: 14, width: getSize().width * 0.4),
                  const SizedBox(height: 6),
                  shimmerBox(height: 12, width: getSize().width * 0.25),
                ],
              ),
            ),
            
            // سهم
            shimmerBox(height: 16, width: 16, radius: 2),
          ],
        ),
      ),
    );
  }

  /// Shimmer للكورس في الشبكة - نسخة محسنة
  static Widget optimizedCourseGridShimmer() {
    return _buildShimmerWrapper(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الكورس
            Expanded(
              flex: 3,
              child: shimmerBox(
                height: double.infinity,
                width: double.infinity,
                radius: 15,
              ),
            ),
            
            // محتوى الكورس
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الكورس
                    shimmerBox(height: 12, width: double.infinity),
                    const SizedBox(height: 6),
                    shimmerBox(height: 10, width: double.infinity * 0.7),
                    
                    const Spacer(),
                    
                    // تقييم وسعر
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        shimmerBox(height: 10, width: 40),
                        shimmerBox(height: 12, width: 50),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shimmer للكورس في القائمة - نسخة محسنة
  static Widget optimizedCourseListShimmer() {
    return _buildShimmerWrapper(
      child: Container(
        height: 120,
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          children: [
            // صورة الكورس
            shimmerBox(height: 96, width: 96, radius: 12),
            
            const SizedBox(width: 12),
            
            // محتوى الكورس
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الكورس
                  shimmerBox(height: 14, width: double.infinity),
                  const SizedBox(height: 6),
                  shimmerBox(height: 12, width: double.infinity * 0.8),
                  
                  const Spacer(),
                  
                  // معلومات إضافية
                  Row(
                    children: [
                      shimmerBox(height: 10, width: 60),
                      const SizedBox(width: 12),
                      shimmerBox(height: 10, width: 40),
                    ],
                  ),
                  
                  const SizedBox(height: 6),
                  
                  // السعر
                  Align(
                    alignment: AlignmentDirectional.centerEnd,
                    child: shimmerBox(height: 14, width: 60),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shimmer للمقال - نسخة محسنة
  static Widget optimizedBlogShimmer() {
    return _buildShimmerWrapper(
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المقال
            shimmerBox(height: 200, width: double.infinity, radius: 15),
            
            // محتوى المقال
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان المقال
                  shimmerBox(height: 16, width: double.infinity),
                  const SizedBox(height: 8),
                  shimmerBox(height: 14, width: double.infinity * 0.8),
                  
                  const SizedBox(height: 12),
                  
                  // وصف المقال
                  shimmerBox(height: 12, width: double.infinity),
                  const SizedBox(height: 6),
                  shimmerBox(height: 12, width: double.infinity),
                  const SizedBox(height: 6),
                  shimmerBox(height: 12, width: double.infinity * 0.6),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات إضافية
                  Row(
                    children: [
                      shimmerBox(height: 10, width: 60),
                      const SizedBox(width: 20),
                      shimmerBox(height: 10, width: 80),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة shimmer محسنة
  static Widget buildOptimizedShimmerList({
    required Widget Function() itemBuilder,
    required int itemCount,
    EdgeInsets? padding,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      padding: padding,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) => RepaintBoundary(
        key: ValueKey('shimmer_$index'),
        child: itemBuilder(),
      ),
    );
  }

  /// بناء شبكة shimmer محسنة
  static Widget buildOptimizedShimmerGrid({
    required Widget Function() itemBuilder,
    required int itemCount,
    required SliverGridDelegate gridDelegate,
    EdgeInsets? padding,
  }) {
    return GridView.builder(
      itemCount: itemCount,
      gridDelegate: gridDelegate,
      padding: padding,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) => RepaintBoundary(
        key: ValueKey('shimmer_grid_$index'),
        child: itemBuilder(),
      ),
    );
  }

  /// Shimmer للصفحة الكاملة
  static Widget fullPageShimmer({
    required String title,
    int categoriesCount = 3,
    int coursesCount = 6,
  }) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          shimmerBox(height: 20, width: 150),
          const SizedBox(height: 16),
          
          // الطبقات الأفقية
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: categoriesCount,
              itemBuilder: (context, index) => optimizedHorizontalCategoryShimmer(),
            ),
          ),
          
          const SizedBox(height: 30),
          
          // عنوان قسم آخر
          shimmerBox(height: 18, width: 200),
          const SizedBox(height: 16),
          
          // الكورسات
          ...List.generate(
            coursesCount,
            (index) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: optimizedCourseListShimmer(),
            ),
          ),
        ],
      ),
    );
  }
} 