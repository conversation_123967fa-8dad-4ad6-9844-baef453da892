package com.mnasaalsultan

import io.flutter.embedding.android.FlutterActivity
import android.os.Bundle
import android.view.WindowManager
import android.app.Activity
import android.content.Context
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.view.Surface
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.util.Log
import android.provider.Settings

class MainActivity: FlutterActivity() {
    private val SCREEN_SECURITY_CHANNEL = "screen_security"
    private val DEVELOPER_MODE_CHANNEL = "developer_mode"
    private var isSecurityEnabled = false
    private var displayManager: DisplayManager? = null
    private var virtualDisplayCallback: VirtualDisplayCallback? = null
    private var isCurrentlyRecording = false

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // إعداد قناة أمان الشاشة الموجودة
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SCREEN_SECURITY_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "enableScreenSecurity" -> {
                    enableScreenSecurity()
                    result.success(true)
                }
                "disableScreenSecurity" -> {
                    disableScreenSecurity()
                    result.success(true)
                }
                "isScreenSecurityEnabled" -> {
                    result.success(isSecurityEnabled)
                }
                "detectScreenRecording" -> {
                    val isRecording = detectScreenRecording()
                    result.success(isRecording)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
        
        // إعداد قناة كشف وضع المطور الجديدة
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DEVELOPER_MODE_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "isDeveloperModeEnabled" -> {
                    val isDeveloperMode = isDeveloperModeEnabled()
                    result.success(isDeveloperMode)
                }
                "getDeveloperModeDetails" -> {
                    val details = getDeveloperModeDetails()
                    result.success(details)
                }
                "checkSpecificSettings" -> {
                    val hasDevSettings = checkSpecificDeveloperSettings()
                    result.success(hasDevSettings)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // تمكين الحماية فور بدء التطبيق
        enableScreenSecurity()
        
        // مراقبة تسجيل الشاشة
        setupScreenRecordingDetection()
    }

    private fun enableScreenSecurity() {
        try {
            // منع لقطة الشاشة والتسجيل
            window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
            
            // حماية إضافية ضد تسجيل الشاشة
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
            }
            
            // منع عرض المحتوى في Recent Apps
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                )
            }
            
            isSecurityEnabled = true
            Log.d("ScreenSecurity", "✅ تم تمكين الحماية الكاملة ضد التسجيل")
            
        } catch (e: Exception) {
            Log.e("ScreenSecurity", "خطأ في تمكين الحماية: ${e.message}")
        }
    }

    private fun disableScreenSecurity() {
        try {
            window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
            isSecurityEnabled = false
            Log.d("ScreenSecurity", "⚠️ تم تعطيل الحماية")
        } catch (e: Exception) {
            Log.e("ScreenSecurity", "خطأ في تعطيل الحماية: ${e.message}")
        }
    }

    private fun setupScreenRecordingDetection() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
                virtualDisplayCallback = VirtualDisplayCallback()
                displayManager?.registerDisplayListener(virtualDisplayCallback!!, null)
                Log.d("ScreenSecurity", "🔍 تم تفعيل مراقبة تسجيل الشاشة")
            }
        } catch (e: Exception) {
            Log.e("ScreenSecurity", "خطأ في تفعيل مراقبة التسجيل: ${e.message}")
        }
    }

    private fun detectScreenRecording(): Boolean {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // فحص Virtual Displays النشطة
                displayManager?.displays?.forEach { display ->
                    if ((display.flags and android.view.Display.FLAG_PRESENTATION) != 0) {
                        Log.w("ScreenSecurity", "🚨 اكتُشف Virtual Display نشط - محتمل تسجيل شاشة!")
                        isCurrentlyRecording = true
                        return true
                    }
                }
                
                // فحص إضافي للأنشطة المشبوهة (يتطلب إذونات خاصة)
                try {
                    val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
                    val runningApps = activityManager.runningAppProcesses
                    
                    for (process in runningApps) {
                        if (isScreenRecordingApp(process.processName)) {
                            Log.w("ScreenSecurity", "🚨 اكتُشف تطبيق تسجيل شاشة نشط: ${process.processName}")
                            isCurrentlyRecording = true
                            return true
                        }
                    }
                } catch (e: Exception) {
                    Log.d("ScreenSecurity", "لا يمكن فحص العمليات الجارية: ${e.message}")
                }
                
                isCurrentlyRecording = false
                return false
            }
        } catch (e: Exception) {
            Log.e("ScreenSecurity", "خطأ في اكتشاف تسجيل الشاشة: ${e.message}")
        }
        return isCurrentlyRecording
    }

    private fun isScreenRecordingApp(packageName: String): Boolean {
        val recordingApps = listOf(
            "com.kimcy929.screenrecorder",
            "com.hecorat.screenrecorder.free",
            "com.nll.screenrecorder",
            "com.iwobanas.screenrecorder.free",
            "com.orpheusdroid.screenrecorder",
            "com.mobizen.mirroring.samsung",
            "com.duapps.recorder",
            "com.androiddevs.screenrecorderadvanced"
        )
        return recordingApps.contains(packageName)
    }

    override fun onResume() {
        super.onResume()
        if (isSecurityEnabled) {
            enableScreenSecurity()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && virtualDisplayCallback != null) {
                displayManager?.unregisterDisplayListener(virtualDisplayCallback!!)
            }
        } catch (e: Exception) {
            Log.e("ScreenSecurity", "خطأ في إلغاء تسجيل المراقب: ${e.message}")
        }
    }

    // ==================== وظائف كشف وضع المطور ====================
    
    /// التحقق من تفعيل وضع المطور
    /// Developer Mode Detection Functions
    private fun isDeveloperModeEnabled(): Boolean {
        return try {
            // الطريقة الأساسية: فحص DEVELOPMENT_SETTINGS_ENABLED
            val developerEnabled = Settings.Global.getInt(
                contentResolver,
                Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0
            ) != 0
            
            // فحص إضافي: ADB_ENABLED
            val adbEnabled = Settings.Global.getInt(
                contentResolver,
                Settings.Global.ADB_ENABLED, 0
            ) != 0
            
            val result = developerEnabled || adbEnabled
            
            if (result) {
                Log.w("DeveloperMode", "🚨 وضع المطور مفعل! Developer: $developerEnabled, ADB: $adbEnabled")
            } else {
                Log.d("DeveloperMode", "✅ وضع المطور غير مفعل")
            }
            
            result
        } catch (e: Exception) {
            Log.e("DeveloperMode", "خطأ في فحص وضع المطور: ${e.message}")
            // في حالة الخطأ، نفترض أن وضع المطور غير مفعل
            false
        }
    }
    
    /// الحصول على تفاصيل شاملة عن وضع المطور
    private fun getDeveloperModeDetails(): Map<String, Any> {
        val details = mutableMapOf<String, Any>()
        
        try {
            // فحص DEVELOPMENT_SETTINGS_ENABLED
            val developerEnabled = Settings.Global.getInt(
                contentResolver,
                Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0
            ) != 0
            
            // فحص ADB_ENABLED  
            val adbEnabled = Settings.Global.getInt(
                contentResolver,
                Settings.Global.ADB_ENABLED, 0
            ) != 0
            
            // فحص USB_DEBUGGING
            val usbDebuggingEnabled = try {
                Settings.Global.getInt(
                    contentResolver,
                    "adb_enabled", 0
                ) != 0
            } catch (e: Exception) {
                false
            }
            
            // فحص خيارات المطور المحددة
            val specificSettings = checkSpecificDeveloperSettings()
            
            details["isDeveloperMode"] = developerEnabled || adbEnabled
            details["developmentSettingsEnabled"] = developerEnabled
            details["adbEnabled"] = adbEnabled
            details["usbDebuggingEnabled"] = usbDebuggingEnabled
            details["hasSpecificDevSettings"] = specificSettings
            details["buildType"] = Build.TYPE
            details["fingerprint"] = Build.FINGERPRINT
            details["model"] = Build.MODEL
            details["checkTime"] = System.currentTimeMillis()
            
            Log.d("DeveloperMode", "تفاصيل وضع المطور: $details")
            
        } catch (e: Exception) {
            Log.e("DeveloperMode", "خطأ في الحصول على تفاصيل وضع المطور: ${e.message}")
            details["error"] = e.message ?: "Unknown error"
            details["isDeveloperMode"] = false
        }
        
        return details
    }
    
    /// فحص إعدادات محددة تشير إلى تطوير
    private fun checkSpecificDeveloperSettings(): Boolean {
        return try {
            var hasDevSettings = false
            
            // قائمة الإعدادات المرتبطة بوضع المطور
            val devSettings = listOf(
                Settings.Global.ADB_ENABLED,
                Settings.Global.DEVELOPMENT_SETTINGS_ENABLED,
                "adb_enabled",
                "enable_gpu_debug_layers",
                "gpu_debug_app",
                "show_touches",
                "pointer_location",
                "show_screen_updates",
                "disable_overlays",
                "show_cpu_usage",
                "force_hw_ui",
                "show_hw_screen_udpates",
                "show_hw_layers_updates",
                "debug_layout",
                "force_rtl_layout_all_locales",
                "transition_animation_scale",
                "window_animation_scale",
                "animator_duration_scale"
            )
            
            for (setting in devSettings) {
                try {
                    val value = when (setting) {
                        // إعدادات Global
                        Settings.Global.ADB_ENABLED,
                        Settings.Global.DEVELOPMENT_SETTINGS_ENABLED -> {
                            Settings.Global.getInt(contentResolver, setting, 0)
                        }
                        // إعدادات System  
                        else -> {
                            try {
                                Settings.System.getInt(contentResolver, setting, 0)
                            } catch (e: Exception) {
                                try {
                                    Settings.Secure.getInt(contentResolver, setting, 0)
                                } catch (e2: Exception) {
                                    0
                                }
                            }
                        }
                    }
                    
                    if (value != 0) {
                        hasDevSettings = true
                        Log.d("DeveloperMode", "إعداد مطور مفعل: $setting = $value")
                        break
                    }
                } catch (e: Exception) {
                    // تجاهل الأخطاء واستكمل الفحص
                    continue
                }
            }
            
            hasDevSettings
        } catch (e: Exception) {
            Log.e("DeveloperMode", "خطأ في فحص الإعدادات المحددة: ${e.message}")
            false
        }
    }

    // فئة لمراقبة Virtual Display (تسجيل الشاشة)
    private inner class VirtualDisplayCallback : DisplayManager.DisplayListener {
        override fun onDisplayAdded(displayId: Int) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val display = displayManager?.getDisplay(displayId)
                if (display != null) {
                    // فحص أكثر دقة للكشف عن تسجيل الشاشة
                    val isVirtualDisplay = (display.flags and android.view.Display.FLAG_PRESENTATION) != 0
                    val isPrivateDisplay = (display.flags and android.view.Display.FLAG_PRIVATE) != 0
                    
                    if (isVirtualDisplay || display.name.contains("overlay", ignoreCase = true) || 
                        display.name.contains("record", ignoreCase = true)) {
                        
                        Log.w("ScreenSecurity", "🚨 تم اكتشاف Virtual Display مشبوه!")
                        Log.w("ScreenSecurity", "Display ID: $displayId, Name: ${display.name}, Flags: ${display.flags}")
                        
                        isCurrentlyRecording = true
                        
                        // تعزيز الحماية فوراً
                        enableScreenSecurity()
                        
                        // إخفاء المحتوى بجعل الشاشة سوداء
                        window.setFlags(
                            WindowManager.LayoutParams.FLAG_SECURE or WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                            WindowManager.LayoutParams.FLAG_SECURE or WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                        )
                    }
                }
            }
        }

        override fun onDisplayRemoved(displayId: Int) {
            Log.d("ScreenSecurity", "تم إزالة عرض: $displayId")
            
            // التحقق من عدم وجود Virtual Displays أخرى
            var hasActiveRecording = false
            displayManager?.displays?.forEach { display ->
                if ((display.flags and android.view.Display.FLAG_PRESENTATION) != 0) {
                    hasActiveRecording = true
                }
            }
            
            if (!hasActiveRecording) {
                isCurrentlyRecording = false
                Log.d("ScreenSecurity", "✅ لا يوجد تسجيل شاشة نشط")
            }
        }

        override fun onDisplayChanged(displayId: Int) {
            // فحص إضافي عند تغيير خصائص العرض
            val display = displayManager?.getDisplay(displayId)
            if (display != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if ((display.flags and android.view.Display.FLAG_PRESENTATION) != 0) {
                    Log.w("ScreenSecurity", "⚠️ تم تغيير خصائص عرض مشبوه: ${display.name}")
                    isCurrentlyRecording = true
                }
            }
        }
    }
}
