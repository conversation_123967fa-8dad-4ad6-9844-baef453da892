name: webinar
description: webinar project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.1+3

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  intl: any

  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.7
  provider: ^6.1.1
  dio: ^5.2.1+1
  persian_number_utility: ^1.1.3
  file_picker: ^8.0.7
  shared_preferences: ^2.1.2
  flutter_native_splash: ^2.3.1
  loading_indicator: ^3.1.0
  get_it: ^7.6.0
  flutter_advanced_drawer: ^1.3.5
  flutter_rating_bar: ^4.0.1
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^7.0.0
  url_launcher: ^6.1.12
  add_2_calendar: any
  http: ^1.1.2
  flutter_widget_from_html: ^0.15.0
  video_player: ^2.8.6
  share_plus: ^10.1.4
  chewie: ^1.7.0
  permission_handler: ^11.3.1
  path_provider: ^2.1.0
  open_file: any
  flutter_image_compress: ^2.0.4
  image_picker: ^1.1.2
  flutter_slidable: ^3.0.0
  webview_flutter: ^4.4.2
  pod_player: ^0.2.2
  flutter_tts: any
  table_calendar: ^3.0.9
  confetti: ^0.7.0
  animated_flip_counter: ^0.3.4
  fl_chart: ^0.68.0
  syncfusion_flutter_pdfviewer: ^24.2.8
  timezone: ^0.9.2
  flutter_inappwebview: ^6.1.5
  lottie: ^3.0.0
  shimmer: ^3.0.0
  firebase_messaging: ^15.2.0
  flutter_local_notifications: any
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  connectivity_plus: ^6.1.2
  cached_network_image: ^3.3.1
  youtube_player_iframe: ^5.1.3
  app_links: ^6.3.3
  crisp_chat: ^2.2.5
  flutter_windowmanager: ^0.2.0
  
  
  
  
dependency_overrides:
  # flutter_widget_from_html_core: ^0.14.9
  # # wakelock: 0.6.2
  # win32: ^5.0.2
  # webview_flutter_android: 3.16.1
  # flutter_inappwebview_android:
  #   git:
  #     url: https://github.com/holzgeist/flutter_inappwebview
  #     path: flutter_inappwebview_android
  #     ref: d89b1d32638b49dfc58c4b7c84153be0c269d057

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.9

flutter_native_splash:
  android: true
  ios: true
  web: false
  color: "#754FFE"
  color_dark: "#754FFE"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/image/flags/
    - assets/image/png/
    - assets/image/svg/
    - assets/image/json/
    - assets/video/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SF-Pro-Bold
      fonts:
        - asset: assets/font/sf-pro-bold.otf
    - family: SF-Pro-Regular
      fonts:
        - asset: assets/font/sf-pro-regular.otf
    
    - family: Vazir-Bold
      fonts:
        - asset: assets/font/vazir-bold.ttf
    - family: Vazir-Regular
      fonts:
        - asset: assets/font/vazir-regular.ttf

    - family: Zain-Regular
      fonts:
        - asset: assets/font/Zain-Regular.ttf
    - family: Zain-Bold
      fonts:
        - asset: assets/font/Zain-Bold.ttf
    - family: Zain-Light
      fonts:
        - asset: assets/font/Zain-Light.ttf

    
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
