# قسم الدورات المتاحة حالياً 📚✨

## 🎯 نظرة عامة

تم إضافة قسم جديد أنيق ومتقدم لعرض "الدورات المتاحة حالياً" في الصفحة الرئيسية لتطبيق منصة السلطان. هذا القسم يعرض أحدث الدورات المتاحة للتسجيل مع تصميم جذاب وتفاعلي.

## ✨ المميزات الرئيسية

### 🎨 التصميم
- **تصميم أنيق ومتسق** مع باقي أقسام التطبيق
- **ألوان متناسقة** مع استخدام اللون الأزرق `#2196F3` كلون أساسي
- **شارات ملونة** تشير إلى حالة الدورة (متاح الآن، خصومات)
- **ظلال وانحناءات** لإضفاء طابع ثلاثي الأبعاد
- **تخطيط مرن** يتناسب مع جميع أحجام الشاشات

### 🔄 الوظائف التفاعلية
- **عرض أفقي** للدورات مع إمكانية التمرير
- **النقر للانتقال** لصفحة تفاصيل الدورة
- **زر "عرض الكل"** للانتقال لصفحة الدورات الكاملة
- **حالات تحميل** مع Shimmer loading
- **معالجة حالة فارغة** عند عدم وجود دورات

### 📊 البيانات المعروضة
- **عنوان الدورة** كاملاً مع معالجة النصوص الطويلة
- **صورة الدورة** مع صورة احتياطية جذابة
- **معلومات المدرس** مع أيقونة
- **التقييم ونجوم** ملونة
- **عدد الطلاب المسجلين**
- **السعر الأصلي والمخفض** مع معالجة الخصومات
- **شارات للعروض والحالات**

## 📁 هيكل الملفات

```
📂 المشروع
├── 📄 lib/app/pages/main_page/home_page/home_page.dart
│   ├── ✅ متغيرات البيانات الجديدة
│   ├── ✅ دالة جلب الدورات المتاحة
│   ├── ✅ Widget قسم الدورات المتاحة
│   ├── ✅ Widget بطاقة الدورة المفردة
│   └── ✅ إضافة القسم في build method
│
└── 📄 AVAILABLE_COURSES_SECTION_GUIDE.md
    └── ✅ التوثيق الشامل
```

## 🔧 التفاصيل التقنية

### 📱 متغيرات البيانات الجديدة

```dart
// في class _HomePageState
bool isLoadingAvailableCoursesData = false;
List<CourseModel> availableCoursesData = [];
```

### 🌐 جلب البيانات

```dart
// في دالة getData()
isLoadingAvailableCoursesData = true;

CourseService.getAll(offset: 0, sort: 'newest').then((value) {
  List<CourseModel> availableCourses = value.where((course) {
    return course.status != 'inactive' && 
           course.price != null && 
           course.price > 0 &&
           course.expired != true;
  }).take(10).toList();
  
  setState(() {
    isLoadingAvailableCoursesData = false;
    availableCoursesData = availableCourses;
  });
});
```

### 🎨 تصميم القسم الرئيسي

```dart
Widget _buildAvailableCoursesSection() {
  return Container(
    margin: padding(horizontal: 20, vertical: 15),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة وشارة "جديد"
        Row(/* ... */),
        
        // عرض الدورات حسب الحالة
        if (isLoadingAvailableCoursesData)
          // شيمر التحميل
        else if (availableCoursesData.isEmpty)
          // حالة فارغة
        else
          // عرض الدورات الفعلية
          SizedBox(
            height: 280,
            child: ListView.builder(/* ... */),
          ),
      ],
    ),
  );
}
```

### 🎯 تصميم بطاقة الدورة

```dart
Widget _buildAvailableCourseCard(CourseModel course) {
  return GestureDetector(
    onTap: () {
      nextRoute('/single-course', arguments: [course.id, course.type == 'bundle']);
    },
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius(radius: 16),
        boxShadow: [/* شادو أنيق */],
      ),
      child: Column(
        children: [
          // صورة الدورة مع الشارات
          Stack(/* ... */),
          
          // معلومات الدورة
          Expanded(
            child: Padding(
              padding: padding(all: 12),
              child: Column(/* ... */),
            ),
          ),
        ],
      ),
    ),
  );
}
```

## 🎨 نظام الألوان المستخدم

| العنصر | اللون | الكود |
|--------|-------|-------|
| **اللون الأساسي** | أزرق | `Color(0xFF2196F3)` |
| **شارة الخصم** | أحمر | `red49` |
| **شارة متاح** | أخضر | `green77()` |
| **التقييم** | أصفر | `yellow29` |
| **النص الرئيسي** | رمادي داكن | `grey33` |
| **النص الثانوي** | رمادي فاتح | `greyB2` |

## 📊 فلترة البيانات

### معايير عرض الدورات:
```dart
course.status != 'inactive'    // دورة نشطة
course.price != null           // لها سعر محدد  
course.price > 0              // ليست مجانية
course.expired != true        // غير منتهية الصلاحية
.take(10)                     // أول 10 دورات فقط
```

## 🔄 حالات العرض المختلفة

### 1️⃣ حالة التحميل
```dart
if (isLoadingAvailableCoursesData)
  SizedBox(
    height: 200,
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Container(
          width: 280,
          margin: EdgeInsets.only(right: 12),
          child: courseSliderItemShimmer(),
        );
      },
    ),
  )
```

### 2️⃣ حالة فارغة
```dart
else if (availableCoursesData.isEmpty)
  Container(
    height: 140,
    width: double.infinity,
    decoration: BoxDecoration(/* ... */),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.school_outlined, color: greyB2, size: 40),
        Text('لا توجد دورات متاحة حالياً'),
      ],
    ),
  )
```

### 3️⃣ حالة العرض العادي
```dart
else
  SizedBox(
    height: 280,
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      physics: const BouncingScrollPhysics(),
      itemCount: availableCoursesData.length,
      itemBuilder: (context, index) {
        return Container(
          width: 240,
          margin: EdgeInsets.only(right: 12),
          child: _buildAvailableCourseCard(availableCoursesData[index]),
        );
      },
    ),
  )
```

## 🎯 التفاعل والتنقل

### النقر على الدورة:
```dart
GestureDetector(
  onTap: () {
    nextRoute(
      '/single-course', 
      arguments: [course.id, course.type == 'bundle']
    );
  },
  child: /* بطاقة الدورة */
)
```

### زر "عرض الكل":
```dart
GestureDetector(
  onTap: () {
    locator<PageProvider>().setPage(PageNames.categories);
  },
  child: Text('عرض الكل'),
)
```

## 🏷️ الشارات والعلامات

### شارة "متاح الآن":
```dart
Container(
  padding: padding(horizontal: 6, vertical: 3),
  decoration: BoxDecoration(
    color: green77(),
    borderRadius: borderRadius(radius: 8),
  ),
  child: Row(
    children: [
      Container(
        width: 4, height: 4,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
      Text('متاح', style: style9Bold().copyWith(color: Colors.white)),
    ],
  ),
)
```

### شارة الخصم:
```dart
if (course.discountPercent != null && course.discountPercent! > 0)
  Container(
    padding: padding(horizontal: 6, vertical: 3),
    decoration: BoxDecoration(
      color: red49,
      borderRadius: borderRadius(radius: 8),
    ),
    child: Text(
      '${course.discountPercent}% خصم',
      style: style9Bold().copyWith(color: Colors.white),
    ),
  )
```

### شارة "جديد":
```dart
Container(
  padding: padding(horizontal: 8, vertical: 4),
  decoration: BoxDecoration(
    color: Color(0xFF2196F3),
    borderRadius: borderRadius(radius: 12),
  ),
  child: Text(
    'جديد',
    style: style10Bold().copyWith(color: Colors.white),
  ),
)
```

## 💰 عرض الأسعار

### السعر مع الخصم:
```dart
if (course.price != null)
  Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      if (course.discountPercent != null && course.discountPercent! > 0)
        Text(
          '${course.price} \$',
          style: style10Regular().copyWith(
            color: greyB2,
            decoration: TextDecoration.lineThrough,
          ),
        ),
      Text(
        '${course.priceWithDiscount ?? course.price} \$',
        style: style13Bold().copyWith(color: Color(0xFF2196F3)),
      ),
    ],
  )
```

## 📍 موضع القسم في الصفحة

```dart
Column(
  children: [
    _buildMainTeacherBanner(),
    _buildHeroBanner(),
    _buildQuickStats(),
    
    // ⬇️ قسم الدورات المتاحة حالياً
    _buildAvailableCoursesSection(),
    
    _buildSpecialOffers(),
    _buildEnhancedFeaturedSection(),
    _buildAchievementsSection(),
    _buildTechnicalSupportSection(),
  ],
)
```

## 🚀 الأداء والتحسينات

### 🔄 تحسينات الأداء:
- **Lazy Loading** للصور
- **Shimmer Loading** أثناء التحميل
- **Efficient ListView** للعرض الأفقي
- **Take(10)** لتحديد عدد العناصر
- **setState محدود** لأجزاء معينة

### 📱 تحسينات UX:
- **BouncingScrollPhysics** للتمرير الطبيعي
- **معالجة حالات الخطأ** والحالات الفارغة
- **تصميم متجاوب** مع جميع الشاشات
- **Visual Feedback** عند النقر

## 🧪 الاختبار

### سيناريوهات الاختبار:
1. **تحميل الدورات بنجاح** ✅
2. **عرض حالة التحميل** ✅
3. **معالجة الحالة الفارغة** ✅
4. **النقر على الدورة** ✅
5. **النقر على عرض الكل** ✅
6. **عرض الخصومات** ✅
7. **عرض معلومات المدرس** ✅

## 📈 إحصائيات النجاح

✅ **تم التطبيق بنجاح** - قسم جديد كامل ومتكامل  
✅ **تصميم متطابق** - يتماشى مع أسلوب التطبيق  
✅ **بيانات حقيقية** - متصل بـ API الفعلي  
✅ **تفاعل كامل** - جميع الأزرار تعمل  
✅ **أداء محسن** - تحميل سريع وسلس  

---

## 📋 ملخص التغييرات

| الإضافة | الوصف | الحالة |
|---------|--------|--------|
| **متغيرات البيانات** | `isLoadingAvailableCoursesData`, `availableCoursesData` | ✅ مضاف |
| **جلب البيانات** | فلترة وتحميل الدورات المتاحة | ✅ مضاف |
| **Widget القسم** | `_buildAvailableCoursesSection()` | ✅ مضاف |
| **Widget البطاقة** | `_buildAvailableCourseCard()` | ✅ مضاف |
| **التكامل** | إضافة القسم في الصفحة الرئيسية | ✅ مضاف |

**النتيجة النهائية: قسم دورات متاحة حالياً متكامل وجاهز! 🎯📚**

---

**تاريخ الإنشاء**: ديسمبر 2024  
**المطور**: مساعد الذكي الاصطناعي  
**الحالة**: ✅ **مكتمل ومختبر** 