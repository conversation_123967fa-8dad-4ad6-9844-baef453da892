import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_windowmanager/flutter_windowmanager.dart';

/// خدمة أمان الشاشة المحسنة لمنع لقطة الشاشة والتسجيل
class ScreenSecurityService {
  static const MethodChannel _channel = MethodChannel('screen_security');
  static bool _isSecurityEnabled = false;
  static bool _isRecordingDetected = false;
  
  /// تمكين حماية الشاشة المحسنة ضد لقطة الشاشة والتسجيل
  static Future<void> enableScreenSecurity() async {
    try {
      if (Platform.isAndroid) {
        // تمكين الحماية الأساسية عبر flutter_windowmanager
        await FlutterWindowManager.addFlags(FlutterWindowManager.FLAG_SECURE);
        
        // تمكين الحماية المحسنة عبر النظام الأصلي
        await _channel.invokeMethod('enableScreenSecurity');
        
        _isSecurityEnabled = true;
        debugPrint('✅ تم تمكين الحماية الكاملة ضد التسجيل والتصوير');
        
      } else if (Platform.isIOS) {
        // لنظام iOS - تطبيق حماية إضافية عبر النظام الأصلي
        await _enableIOSScreenSecurity();
        _isSecurityEnabled = true;
        debugPrint('✅ تم تمكين حماية الشاشة لـ iOS');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تمكين حماية الشاشة: $e');
    }
  }
  
  /// تعطيل حماية الشاشة (للاستخدام في حالات خاصة)
  static Future<void> disableScreenSecurity() async {
    try {
      if (Platform.isAndroid) {
        await FlutterWindowManager.clearFlags(FlutterWindowManager.FLAG_SECURE);
        await _channel.invokeMethod('disableScreenSecurity');
        _isSecurityEnabled = false;
        debugPrint('⚠️ تم تعطيل حماية الشاشة للأندرويد');
      } else if (Platform.isIOS) {
        await _disableIOSScreenSecurity();
        _isSecurityEnabled = false;
        debugPrint('⚠️ تم تعطيل حماية الشاشة لـ iOS');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تعطيل حماية الشاشة: $e');
    }
  }
  
  /// التحقق من حالة حماية الشاشة
  static Future<bool> isScreenSecurityEnabled() async {
    try {
      if (Platform.isAndroid) {
        // التحقق من الحالة عبر النظام الأصلي
        bool nativeStatus = await _channel.invokeMethod('isScreenSecurityEnabled');
        return nativeStatus && _isSecurityEnabled;
      } else if (Platform.isIOS) {
        return await _isIOSScreenSecurityEnabled();
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة حماية الشاشة: $e');
    }
    return _isSecurityEnabled;
  }
  
  /// اكتشاف تسجيل الشاشة (Android فقط)
  static Future<bool> detectScreenRecording() async {
    try {
      if (Platform.isAndroid) {
        // هذه الوظيفة ستكتشف تسجيل الشاشة عبر مراقبة Virtual Displays
        bool isRecording = await _channel.invokeMethod('detectScreenRecording') ?? false;
        
        if (isRecording && !_isRecordingDetected) {
          _isRecordingDetected = true;
          debugPrint('🚨 تم اكتشاف تسجيل شاشة! تم تعزيز الحماية');
          
          // تعزيز الحماية فوراً
          await enableScreenSecurity();
          
          // إشعار المطور
          _notifyRecordingDetected();
        } else if (!isRecording && _isRecordingDetected) {
          _isRecordingDetected = false;
          debugPrint('✅ تم توقف تسجيل الشاشة');
        }
        
        return isRecording;
      }
    } catch (e) {
      debugPrint('❌ خطأ في اكتشاف تسجيل الشاشة: $e');
    }
    return false;
  }
  
  /// مراقبة مستمرة لتسجيل الشاشة
  static Future<void> startRecordingMonitoring() async {
    if (Platform.isAndroid) {
      // بدء مراقبة تسجيل الشاشة كل ثانيتين
      Stream.periodic(const Duration(seconds: 2)).listen((_) async {
        await detectScreenRecording();
      });
      
      debugPrint('🔍 تم بدء المراقبة المستمرة لتسجيل الشاشة');
    }
  }
  
  /// إشعار عند اكتشاف تسجيل الشاشة
  static void _notifyRecordingDetected() {
    // يمكن إضافة إشعار للمستخدم أو إجراء محدد
    debugPrint('⚠️ تحذير: تم اكتشاف محاولة تسجيل للمحتوى المحمي');
  }
  
  /// تمكين حماية الشاشة لـ iOS عبر النظام الأصلي
  static Future<void> _enableIOSScreenSecurity() async {
    try {
      await _channel.invokeMethod('enableScreenSecurity');
    } on PlatformException catch (e) {
      debugPrint('خطأ في تمكين حماية الشاشة لـ iOS: ${e.message}');
    }
  }
  
  /// تعطيل حماية الشاشة لـ iOS عبر النظام الأصلي
  static Future<void> _disableIOSScreenSecurity() async {
    try {
      await _channel.invokeMethod('disableScreenSecurity');
    } on PlatformException catch (e) {
      debugPrint('خطأ في تعطيل حماية الشاشة لـ iOS: ${e.message}');
    }
  }
  
  /// التحقق من حالة حماية الشاشة لـ iOS
  static Future<bool> _isIOSScreenSecurityEnabled() async {
    try {
      final bool isEnabled = await _channel.invokeMethod('isScreenSecurityEnabled');
      return isEnabled;
    } on PlatformException catch (e) {
      debugPrint('خطأ في التحقق من حالة حماية الشاشة لـ iOS: ${e.message}');
      return false;
    }
  }
  
  /// تطبيق حماية الشاشة على صفحة معينة
  static Future<void> protectCurrentScreen() async {
    await enableScreenSecurity();
    
    // بدء مراقبة التسجيل للصفحة الحالية
    if (Platform.isAndroid) {
      await startRecordingMonitoring();
    }
  }
  
  /// إزالة حماية الشاشة من صفحة معينة
  static Future<void> unprotectCurrentScreen() async {
    await disableScreenSecurity();
  }
  
  /// تمكين حماية الشاشة مع معالجة الأخطاء المحسنة
  static Future<bool> enableScreenSecuritySafe() async {
    try {
      await enableScreenSecurity();
      
      // التحقق من نجاح العملية
      bool isEnabled = await isScreenSecurityEnabled();
      if (isEnabled) {
        debugPrint('✅ تم تمكين حماية الشاشة الكاملة بنجاح');
        
        // بدء مراقبة التسجيل
        await startRecordingMonitoring();
        
        return true;
      } else {
        debugPrint('⚠️ فشل في تمكين حماية الشاشة');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في تمكين حماية الشاشة الآمن: $e');
      return false;
    }
  }
  
  /// الحصول على حالة الحماية الحالية
  static bool get isCurrentlySecured => _isSecurityEnabled;
  
  /// الحصول على حالة اكتشاف التسجيل
  static bool get isRecordingDetected => _isRecordingDetected;
  
  /// تفعيل الحماية القصوى (لقطة الشاشة + تسجيل الفيديو)
  static Future<void> enableMaximumSecurity() async {
    debugPrint('🛡️ تفعيل الحماية القصوى...');
    
    await enableScreenSecurity();
    await startRecordingMonitoring();
    
    debugPrint('✅ تم تفعيل الحماية القصوى ضد التصوير والتسجيل');
  }
} 