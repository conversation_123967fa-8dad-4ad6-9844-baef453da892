import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:webinar/app/models/content_model.dart';
import 'package:webinar/app/models/course_model.dart';
import 'package:webinar/app/models/notice_model.dart';
import 'package:webinar/app/models/single_course_model.dart';
import 'package:webinar/app/models/user_model.dart';
import 'package:webinar/common/data/api_public_data.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/common/utils/cache_manager.dart';
import 'package:webinar/common/utils/constants.dart';
import 'package:http/http.dart';
import 'package:webinar/common/utils/http_handler.dart';
import '../../../common/enums/error_enum.dart';
import '../../../common/utils/error_handler.dart';
import '../../../common/components.dart';
import '../../models/single_content_model.dart';
import 'package:webinar/common/utils/cache_manager.dart';

/// خدمة الكورسات المحسنة مع التخزين المؤقت
class CourseServiceEnhanced {

  /// الحصول على جميع الكورسات مع تخزين مؤقت
  static Future<List<CourseModel>> getAll({
    required int offset,
    bool upcoming = false,
    bool free = false,
    bool discount = false,
    bool downloadable = false,
    String? sort,
    String? type,
    String? cat,
    bool reward = false,
    bool bundle = false,
    List<int>? filterOption,
  }) async {
    
    // إنشاء مفتاح التخزين المؤقت بناءً على المعاملات
    final cacheKey = _buildCacheKey('courses', {
      'offset': offset,
      'upcoming': upcoming,
      'free': free,
      'discount': discount,
      'downloadable': downloadable,
      'sort': sort,
      'type': type,
      'cat': cat,
      'reward': reward,
      'bundle': bundle,
      'filterOption': filterOption,
    });

    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    if (offset == 0) { // فقط للصفحة الأولى
      final cachedData = await CacheManager.instance.getList<Map<String, dynamic>>(cacheKey);
      if (cachedData != null) {
        try {
          return cachedData.map((json) => CourseModel.fromJson(json)).toList();
        } catch (e) {
          debugPrint('خطأ في تحويل الكورسات المحفوظة: $e');
        }
      }
    }

    // تحميل البيانات من الخادم
    List<CourseModel> data = [];
    try {
      String url = '${Constants.baseUrl}${bundle ? 'bundles' : 'courses'}?offset=$offset&limit=10';

      if (upcoming) url += '&upcoming=1';
      if (free) url += '&free=1';
      if (discount) url += '&discount=1';
      if (downloadable) url += '&downloadable=1';
      if (reward) url += '&reward=1';

      if (sort != null) url += '&sort=$sort';
      if (cat != null) url += '&cat=$cat';

      if (filterOption != null && filterOption.isNotEmpty) {
        for (int i = 0; i < filterOption.length; i++) {
          url += '&filter_option=${filterOption[i]}';
        }
      }

      Response res = await httpGet(url);
      var jsonRes = jsonDecode(res.body);

      if (jsonRes['success'] ?? false) {
        final coursesJson = <Map<String, dynamic>>[];

        if (bundle) {
          jsonRes['data']['bundles'].forEach((json) {
            final course = CourseModel.fromJson(json);
            data.add(course);
            coursesJson.add(json);
          });
        } else {
          jsonRes['data'].forEach((json) {
            final course = CourseModel.fromJson(json);
            data.add(course);
            coursesJson.add(json);
          });
        }

        // حفظ في التخزين المؤقت للصفحة الأولى فقط
        if (offset == 0 && data.isNotEmpty) {
          int cacheMinutes = _getCacheMinutes(sort);
          await CacheManager.instance.storeList(
            cacheKey,
            coursesJson,
            expiryMinutes: cacheMinutes,
          );
        }

        log('course count : ${data.length}');
        return data;
      } else {
        return data;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الكورسات: $e');
      return data;
    }
  }

  /// الحصول على الكورسات المميزة مع تخزين مؤقت
  static Future<List<CourseModel>> featuredCourse({String? cat}) async {
    final cacheKey = cat != null 
        ? '${CacheKeys.featuredCourses}_cat_$cat'
        : CacheKeys.featuredCourses;

    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    final cachedData = await CacheManager.instance.getList<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      try {
        return cachedData.map((json) => CourseModel.fromJson(json)).toList();
      } catch (e) {
        debugPrint('خطأ في تحويل الكورسات المميزة المحفوظة: $e');
      }
    }

    // تحميل البيانات من الخادم
    List<CourseModel> data = [];
    try {
      String url = '${Constants.baseUrl}featured-courses';
      if (cat != null) url += '?cat=$cat';

      Response res = await httpGet(url, isMaintenance: true);
      var jsonRes = jsonDecode(res.body);

      if (jsonRes['success']) {
        final coursesJson = <Map<String, dynamic>>[];

        jsonRes['data'].forEach((json) {
          final course = CourseModel.fromJson(json);
          data.add(course);
          coursesJson.add(json);
        });

        // حفظ في التخزين المؤقت لمدة ساعة
        await CacheManager.instance.storeList(
          cacheKey,
          coursesJson,
          expiryMinutes: 60,
        );

        log('featured course count : ${data.length}');
        return data;
      } else {
        return data;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الكورسات المميزة: $e');
      return data;
    }
  }

  /// الحصول على تفاصيل كورس واحد مع تخزين مؤقت
  static Future<SingleCourseModel?> getSingleCourseData(
    int id,
    bool isBundle, {
    bool isPrivate = false,
  }) async {
    final cacheKey = 'cache_single_course_${id}_${isBundle}_$isPrivate';

    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    final cachedData = await CacheManager.instance.getData<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      try {
        return SingleCourseModel.fromJson(cachedData);
      } catch (e) {
        debugPrint('خطأ في تحويل تفاصيل الكورس المحفوظة: $e');
      }
    }

    // تحميل البيانات من الخادم
    try {
      String url = '${Constants.baseUrl}${isPrivate ? 'panel/webinars' : isBundle ? 'bundles' : 'courses'}/$id';

      Response res = await httpGet(url, isSendToken: true);
      var jsonRes = jsonDecode(res.body);

      if (jsonRes['success'] ?? false) {
        final courseData = isBundle ? jsonRes['data']['bundle'] : jsonRes['data'];
        final course = SingleCourseModel.fromJson(courseData);

        // حفظ في التخزين المؤقت لمدة 30 دقيقة
        await CacheManager.instance.storeData(
          cacheKey,
          courseData,
          expiryMinutes: 30,
        );

        return course;
      } else {
        ErrorHandler().showError(ErrorEnum.error, jsonRes, readMessage: true);
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل الكورس: $e');
      showSnackBar(ErrorEnum.error, null, desc: appText.serverExceptionError);
      return null;
    }
  }

  /// الحصول على محتوى الكورس مع تخزين مؤقت
  static Future<List<ContentModel>?> getCourseContent(int courseId) async {
    final cacheKey = CacheKeys.courseContent(courseId);

    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    final cachedData = await CacheManager.instance.getList<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      try {
        return cachedData.map((json) => ContentModel.fromJson(json)).toList();
      } catch (e) {
        debugPrint('خطأ في تحويل محتوى الكورس المحفوظ: $e');
      }
    }

    // تحميل البيانات من الخادم
    try {
      String url = '${Constants.baseUrl}panel/webinars/$courseId/chapters';

      Response res = await httpGet(url, isSendToken: true);
      var jsonRes = jsonDecode(res.body);

      if (jsonRes['success']) {
        final List<ContentModel> contents = [];
        final contentsJson = <Map<String, dynamic>>[];

        jsonRes['data'].forEach((json) {
          final content = ContentModel.fromJson(json);
          contents.add(content);
          contentsJson.add(json);
        });

        // حفظ في التخزين المؤقت لمدة ساعة
        await CacheManager.instance.storeList(
          cacheKey,
          contentsJson,
          expiryMinutes: 60,
        );

        return contents;
      } else {
        ErrorHandler().showError(ErrorEnum.error, jsonRes);
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل محتوى الكورس: $e');
      return null;
    }
  }

  /// تحديث بيانات الكورسات (إزالة التخزين المؤقت وإعادة التحميل)
  static Future<void> refreshCoursesData() async {
    final keysToRemove = [
      CacheKeys.featuredCourses,
      CacheKeys.newCourses,
      CacheKeys.bestRatedCourses,
      CacheKeys.bestSellingCourses,
      CacheKeys.discountCourses,
      CacheKeys.freeCourses,
    ];

    for (final key in keysToRemove) {
      await CacheManager.instance.removeData(key);
    }
  }

  /// تحميل البيانات الأساسية مسبقاً
  static Future<void> preloadEssentialData() async {
    try {
      // تحميل البيانات الأساسية بشكل متوازي
      await Future.wait([
        featuredCourse(),
        getAll(offset: 0, sort: 'newest'),
        getAll(offset: 0, sort: 'best_rates'),
        getAll(offset: 0, sort: 'bestsellers'),
        getAll(offset: 0, discount: true),
        getAll(offset: 0, free: true),
      ]);

      debugPrint('تم تحميل البيانات الأساسية للكورسات مسبقاً');
    } catch (e) {
      debugPrint('خطأ في التحميل المسبق للكورسات: $e');
    }
  }

  /// حذف تخزين كورس معين
  static Future<void> clearCourseCache(int courseId) async {
    await CacheManager.instance.removeData(
      CacheKeys.courseContent(courseId)
    );
    await CacheManager.instance.removeData('cache_single_course_${courseId}_false_false');
    await CacheManager.instance.removeData('cache_single_course_${courseId}_true_false');
    await CacheManager.instance.removeData('cache_single_course_${courseId}_false_true');
  }

  /// بناء مفتاح التخزين المؤقت
  static String _buildCacheKey(String prefix, Map<String, dynamic> params) {
    final sortedParams = Map.fromEntries(
      params.entries.where((e) => e.value != null).toList()
        ..sort((a, b) => a.key.compareTo(b.key))
    );

    final paramsString = sortedParams.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    return 'cache_${prefix}_${paramsString.hashCode}';
  }

  /// تحديد مدة التخزين المؤقت بناءً على نوع البيانات
  static int _getCacheMinutes(String? sort) {
    switch (sort) {
      case 'newest':
        return 15; // 15 دقيقة للكورسات الجديدة
      case 'best_rates':
      case 'bestsellers':
        return 60; // ساعة للكورسات الأعلى تقييماً والأكثر مبيعاً
      default:
        return 30; // 30 دقيقة افتراضي
    }
  }

  /// فحص حالة التخزين المؤقت
  static Future<Map<String, bool>> getCacheStatus() async {
    return {
      'featuredCourses': await CacheManager.instance.hasValidData(
        CacheKeys.featuredCourses
      ),
      'newCourses': await CacheManager.instance.hasValidData(
        CacheKeys.newCourses
      ),
      'bestRatedCourses': await CacheManager.instance.hasValidData(
        CacheKeys.bestRatedCourses
      ),
      'bestSellingCourses': await CacheManager.instance.hasValidData(
        CacheKeys.bestSellingCourses
      ),
      'discountCourses': await CacheManager.instance.hasValidData(
        CacheKeys.discountCourses
      ),
      'freeCourses': await CacheManager.instance.hasValidData(
        CacheKeys.freeCourses
      ),
    };
  }

  /// الحصول على إحصائيات استخدام التخزين المؤقت
  static Future<Map<String, dynamic>> getCacheStatistics() async {
    final stats = await CacheManager.instance.getCacheStats();
    final cacheStatus = await getCacheStatus();
    
    return {
      ...stats,
      'coursesCacheStatus': cacheStatus,
    };
  }
} 