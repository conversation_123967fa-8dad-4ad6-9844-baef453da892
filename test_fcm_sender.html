<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إرسال إشعارات FCM</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e2f3ff;
            color: #0c5460;
            border: 1px solid #b8daff;
            margin-bottom: 20px;
        }
        .token-input {
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 اختبار إرسال إشعارات Firebase</h1>
        
        <div class="info">
            <strong>تعليمات:</strong><br>
            1. احصل على FCM Token من التطبيق<br>
            2. احصل على Server Key من Firebase Console<br>
            3. املأ النموذج أدناه واضغط إرسال
        </div>

        <form id="fcmForm">
            <div class="form-group">
                <label for="serverKey">Server Key (من Firebase Console):</label>
                <input type="password" id="serverKey" placeholder="AAAAxxxxxxx..." required>
            </div>

            <div class="form-group">
                <label for="fcmToken">FCM Token (من التطبيق):</label>
                <textarea id="fcmToken" class="token-input" placeholder="eBp7GP-rQ8u0CzKPqTfAd7:APA91bEy..." required></textarea>
            </div>

            <div class="form-group">
                <label for="title">عنوان الإشعار:</label>
                <input type="text" id="title" value="اختبار إشعار" required>
            </div>

            <div class="form-group">
                <label for="body">محتوى الإشعار:</label>
                <textarea id="body" required>هذا إشعار تجريبي من Firebase Cloud Messaging</textarea>
            </div>

            <div class="form-group">
                <label for="notificationType">نوع الإشعار:</label>
                <select id="notificationType">
                    <option value="general">عام</option>
                    <option value="course">كورس</option>
                    <option value="assignment">مهمة</option>
                    <option value="quiz">اختبار</option>
                    <option value="meeting">اجتماع</option>
                    <option value="message">رسالة</option>
                    <option value="certificate">شهادة</option>
                </select>
            </div>

            <div class="form-group">
                <label for="dataId">معرف البيانات (اختياري):</label>
                <input type="text" id="dataId" placeholder="123" value="test_123">
            </div>

            <button type="submit" id="sendBtn">📤 إرسال الإشعار</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('fcmForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const sendBtn = document.getElementById('sendBtn');
            const result = document.getElementById('result');
            
            sendBtn.disabled = true;
            sendBtn.textContent = '⏳ جاري الإرسال...';
            
            const serverKey = document.getElementById('serverKey').value;
            const fcmToken = document.getElementById('fcmToken').value;
            const title = document.getElementById('title').value;
            const body = document.getElementById('body').value;
            const notificationType = document.getElementById('notificationType').value;
            const dataId = document.getElementById('dataId').value;
            
            const payload = {
                to: fcmToken,
                notification: {
                    title: title,
                    body: body,
                    sound: "default",
                    badge: "1"
                },
                data: {
                    type: notificationType,
                    id: dataId,
                    click_action: "FLUTTER_NOTIFICATION_CLICK"
                },
                priority: "high",
                content_available: true
            };
            
            try {
                const response = await fetch('https://fcm.googleapis.com/fcm/send', {
                    method: 'POST',
                    headers: {
                        'Authorization': `key=${serverKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const responseData = await response.json();
                
                if (response.ok && responseData.success === 1) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <strong>✅ تم إرسال الإشعار بنجاح!</strong><br>
                        Message ID: ${responseData.results[0].message_id}<br>
                        تحقق من هاتفك الآن
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <strong>❌ فشل في إرسال الإشعار</strong><br>
                        ${responseData.results ? responseData.results[0].error : responseData.error || 'خطأ غير معروف'}
                    `;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `
                    <strong>❌ خطأ في الشبكة</strong><br>
                    ${error.message}
                `;
            }
            
            result.style.display = 'block';
            sendBtn.disabled = false;
            sendBtn.textContent = '📤 إرسال الإشعار';
        });
    </script>
</body>
</html> 