import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart';
import 'package:webinar/app/models/category_model.dart';
import 'package:webinar/app/models/filter_model.dart';
import 'package:webinar/common/utils/cache_manager.dart';
import '../../../common/enums/error_enum.dart';
import '../../../common/utils/constants.dart';
import '../../../common/utils/error_handler.dart';
import '../../../common/utils/http_handler.dart';

class CategoriesService{

  /// الحصول على الطبقات الشائعة مع تخزين مؤقت
  static Future<List<CategoryModel>> trendCategories() async {
    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    final cachedData = await CacheManager.instance.getList<Map<String, dynamic>>(
      CacheKeys.trendCategories
    );
    
    if (cachedData != null) {
      try {
        return cachedData.map((json) => CategoryModel.fromJson(json)).toList();
      } catch (e) {
        // في حالة فشل تحويل البيانات المحفوظة، نتجاهلها ونحمل البيانات الجديدة
        debugPrint('خطأ في تحويل الطبقات الشائعة المحفوظة: $e');
      }
    }

    // تحميل البيانات من الخادم
    List<CategoryModel> data = [];
    try {
      String url = '${Constants.baseUrl}trend-categories';

      Response res = await httpGet(url);

      log(res.body.toString());

      var jsonResponse = jsonDecode(res.body);
      if (jsonResponse['success']) {
        final categoriesJson = <Map<String, dynamic>>[];
        
        jsonResponse['data']['categories'].forEach((json) {
          final category = CategoryModel.fromJson(json);
          data.add(category);
          categoriesJson.add(json);
        });
        
        // حفظ البيانات في التخزين المؤقت
        await CacheManager.instance.storeList(
          CacheKeys.trendCategories,
          categoriesJson,
          expiryMinutes: 60 // ساعة واحدة
        );
        
        return data;
      } else {
        ErrorHandler().showError(ErrorEnum.error, jsonResponse);
        return data;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الطبقات الشائعة: $e');
      return data;
    }
  }

  /// الحصول على جميع الطبقات مع تخزين مؤقت
  static Future<List<CategoryModel>> categories() async {
    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    final cachedData = await CacheManager.instance.getList<Map<String, dynamic>>(
      CacheKeys.categories
    );
    
    if (cachedData != null) {
      try {
        return cachedData.map((json) => CategoryModel.fromJson(json)).toList();
      } catch (e) {
        debugPrint('خطأ في تحويل الطبقات المحفوظة: $e');
      }
    }

    // تحميل البيانات من الخادم
    List<CategoryModel> data = [];
    try {
      String url = '${Constants.baseUrl}categories';

      Response res = await httpGet(url);

      var jsonResponse = jsonDecode(res.body);
      if (jsonResponse['success']) {
        final categoriesJson = <Map<String, dynamic>>[];
        
        jsonResponse['data']['categories'].forEach((json) {
          final category = CategoryModel.fromJson(json);
          data.add(category);
          categoriesJson.add(json);
        });
        
        // حفظ البيانات في التخزين المؤقت لمدة 2 ساعة
        await CacheManager.instance.storeList(
          CacheKeys.categories,
          categoriesJson,
          expiryMinutes: 120
        );
        
        return data;
      } else {
        ErrorHandler().showError(ErrorEnum.error, jsonResponse);
        return data;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الطبقات: $e');
      return data;
    }
  }
   
  /// الحصول على فلاتر طبقة معينة مع تخزين مؤقت
  static Future<List<FilterModel>> getFilters(int id) async {
    final cacheKey = CacheKeys.categoryFilter(id);
    
    // محاولة الحصول على البيانات من التخزين المؤقت أولاً
    final cachedData = await CacheManager.instance.getList<Map<String, dynamic>>(cacheKey);
    
    if (cachedData != null) {
      try {
        return cachedData.map((json) => FilterModel.fromJson(json)).toList();
      } catch (e) {
        debugPrint('خطأ في تحويل فلاتر الطبقة المحفوظة: $e');
      }
    }

    // تحميل البيانات من الخادم
    List<FilterModel> data = [];
    try {
      String url = '${Constants.baseUrl}categories/$id/webinars';

      Response res = await httpGet(url);

      var jsonResponse = jsonDecode(res.body);
      if (jsonResponse['success']) {
        final filtersJson = <Map<String, dynamic>>[];
        
        jsonResponse['data']['filters'].forEach((json) {
          final filter = FilterModel.fromJson(json);
          data.add(filter);
          filtersJson.add(json);
        });
        
        // حفظ البيانات في التخزين المؤقت لمدة 30 دقيقة
        await CacheManager.instance.storeList(
          cacheKey,
          filtersJson,
          expiryMinutes: 30
        );
        
        return data;
      } else {
        ErrorHandler().showError(ErrorEnum.error, jsonResponse);
        return data;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل فلاتر الطبقة: $e');
      return data;
    }
  }

  /// تحديث بيانات الطبقات (إزالة التخزين المؤقت وإعادة التحميل)
  static Future<void> refreshCategories() async {
    await CacheManager.instance.removeData(CacheKeys.categories);
    await CacheManager.instance.removeData(CacheKeys.trendCategories);
    
    // إعادة تحميل البيانات
    await Future.wait([
      categories(),
      trendCategories(),
    ]);
  }

  /// تنظيف تخزين طبقة معينة
  static Future<void> clearCategoryCache(int categoryId) async {
    await CacheManager.instance.removeData(
      CacheKeys.categoryFilter(categoryId)
    );
    await CacheManager.instance.removeData(
      CacheKeys.coursesForCategory(categoryId)
    );
  }

  /// تحميل البيانات مسبقاً في الخلفية
  static Future<void> preloadCategoriesData() async {
    try {
      // تحميل البيانات الأساسية بشكل متوازي
      await Future.wait([
        categories(),
        trendCategories(),
      ]);
      
      debugPrint('تم تحميل بيانات الطبقات مسبقاً');
    } catch (e) {
      debugPrint('خطأ في التحميل المسبق للطبقات: $e');
    }
  }

  /// فحص حالة التخزين المؤقت للطبقات
  static Future<Map<String, bool>> getCacheStatus() async {
    return {
      'categories': await CacheManager.instance.hasValidData(CacheKeys.categories),
      'trendCategories': await CacheManager.instance.hasValidData(CacheKeys.trendCategories),
    };
  }
}



