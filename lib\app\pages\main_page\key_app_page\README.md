# صفحة تطبيق المفتاح (Key App Page)

## الوصف
هذه الصفحة تحل مكان صفحة "مقدمي الخدمات" في الشاشة الرئيسية للتطبيق. تهدف إلى توفير نظام مفاتيح الاشتراك وإرشاد المستخدمين لشراء المفاتيح من المكتبات المتوفرة.

## الميزات الرئيسية

### 1. إدخال مفتاح الاشتراك
- حقل نصي لإدخال مفتاح الاشتراك
- تصميم مميز مع واجهة مستخدم جذابة
- التحقق من صحة المفتاح

### 2. شراء المفاتيح من المكتبات
- عرض قائمة المكتبات المتوفرة مقسمة حسب المحافظات
- معلومات تفصيلية عن كل مكتبة (الاسم، العنوان، رقم الهاتف)
- إمكانية عرض موقع المكتبة على الخريطة

### 3. التواصل مع خدمة العملاء
- إمكانية التواصل عبر الواتساب
- إمكانية الاتصال الهاتفي
- واجهة سهلة للوصول إلى الدعم

## هيكل الملفات

```
key_app_page/
├── key_app_page.dart          # الصفحة الرئيسية لتطبيق المفتاح
├── libraries_page/
│   └── libraries_page.dart    # صفحة عرض المكتبات
└── README.md                  # هذا الملف
```

## النماذج المستخدمة

### LibraryModel
```dart
class LibraryModel {
  final int id;
  final String name;
  final String phone;
  final String address;
  final String governorate;
  final double? latitude;
  final double? longitude;
}
```

## البيانات النموذجية المتوفرة

### محافظة رصافة
- مكتبة المستنصرية

### محافظة كرخ
- مكتبة الجوهرة (البتول)
- مكتبة باركود
- مكتبة مارينا
- المتاسنر 2
- مكتبة الاحسان
- مكتبة الربيعي

### المحافظات الأخرى
- الجذور - البصرة
- الذهبي - ديالى
- التاج - بابل
- الصديقين - صلاح الدين
- اشرف وخلدون - ميسان
- كنانة - موصل

## التطوير المستقبلي

### المميزات المخطط إضافتها:
1. **تكامل الخريطة**: إضافة خرائط Google أو Apple لعرض المواقع الفعلية
2. **API الخلفي**: ربط البيانات بواجهة برمجة التطبيقات الخلفية
3. **التحقق من المفاتيح**: نظام فعلي للتحقق من صحة مفاتيح الاشتراك
4. **الإشعارات**: تنبيهات عند انتهاء صلاحية المفتاح
5. **تتبع الاستخدام**: إحصائيات عن استخدام المفاتيح

## التخصيص

### تخصيص الألوان والتصميم
يمكن تخصيص ألوان وتصميم الصفحة من خلال:
- `config/colors.dart` - للألوان
- `config/styles.dart` - للنصوص والخطوط

### إضافة مكتبات جديدة
لإضافة مكتبات جديدة، قم بتعديل الخريطة `librariesByGovernorate` في ملف `libraries_page.dart`

## التبعيات المطلوبة
- `flutter/material.dart`
- `provider` للإدارة الحالة
- `flutter_svg` للرموز
- خدمات التوطين للنصوص متعددة اللغات

## الاختبارات
تم إنشاء ملف اختبار أساسي في `test/key_app_test.dart` للتحقق من:
- بناء الصفحة بدون أخطاء
- وظائف حقل إدخال النص 