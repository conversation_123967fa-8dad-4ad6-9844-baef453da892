import 'package:flutter/material.dart';
import 'package:webinar/app/models/filter_model.dart';

class FilterCourseProvider extends ChangeNotifier{

  List<FilterModel> filters = [];
  List<int> filterSelected = [];

  bool upcoming=true;
  bool free=true;
  bool discount=true;
  bool downloadable=true;
  bool bundleCourse=true;
  bool rewardCourse=false;

  String sort = '';


  clearFilter(){
    filterSelected.clear();

    upcoming=true;
    free=true;
    discount=true;
    downloadable=true;
    bundleCourse=true;
    rewardCourse=false;

    sort = '';
  }
}

