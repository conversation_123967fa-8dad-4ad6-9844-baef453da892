import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// خدمة كشف وضع المطور - منع استخدام التطبيق إذا كان وضع المطور مفعلاً
/// Developer Mode Detection Service
class DeveloperModeService {
  static const MethodChannel _channel = MethodChannel('developer_mode');
  
  /// متغير للتحكم في تنفيذ الفحص (يتم تعطيله في Debug mode)
  static bool get _shouldCheckDeveloperMode {
    // في وضع التطوير (Debug mode) لا نقوم بالفحص
    // في وضع الإنتاج (Release mode) نقوم بالفحص
    return kReleaseMode;
  }
  
  /// التحقق من حالة وضع المطور
  /// Returns true إذا كان وضع المطور مفعلاً
  static Future<bool> isDeveloperModeEnabled() async {
    try {
      // إذا كنا في وضع التطوير (Debug mode)، نعيد false دائماً
      if (!_shouldCheckDeveloperMode) {
        debugPrint('🔧 Debug Mode: تم تجاهل فحص وضع المطور');
        return false;
      }
      
      if (Platform.isAndroid) {
        // فحص وضع المطور على Android
        final bool isDeveloperMode = await _channel.invokeMethod('isDeveloperModeEnabled') ?? false;
        
        if (isDeveloperMode) {
          debugPrint('⚠️ تم اكتشاف تفعيل وضع المطور!');
        } else {
          debugPrint('✅ وضع المطور غير مفعل');
        }
        
        return isDeveloperMode;
      } else if (Platform.isIOS) {
        // على iOS، نتحقق من وجود أدوات التطوير
        final bool isDeveloperMode = await _checkIOSDeveloperMode();
        
        if (isDeveloperMode) {
          debugPrint('⚠️ تم اكتشاف أدوات تطوير iOS!');
        } else {
          debugPrint('✅ أدوات تطوير iOS غير مفعلة');
        }
        
        return isDeveloperMode;
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص وضع المطور: $e');
      // في حالة حدوث خطأ، نفترض أن وضع المطور غير مفعل
      return false;
    }
    
    return false;
  }
  
  /// فحص وضع المطور بشكل دوري
  /// Returns Stream يرسل true عندما يتم اكتشاف وضع المطور
  static Stream<bool> startDeveloperModeMonitoring() async* {
    if (!_shouldCheckDeveloperMode) {
      debugPrint('🔧 Debug Mode: تم تجاهل مراقبة وضع المطور');
      return;
    }
    
    debugPrint('🔍 بدء مراقبة وضع المطور...');
    
    while (true) {
      try {
        final bool isDeveloperMode = await isDeveloperModeEnabled();
        yield isDeveloperMode;
        
        // فحص كل 3 ثوان
        await Future.delayed(const Duration(seconds: 3));
      } catch (e) {
        debugPrint('❌ خطأ في مراقبة وضع المطور: $e');
        // في حالة الخطأ، ننتظر 5 ثوان ونحاول مرة أخرى
        await Future.delayed(const Duration(seconds: 5));
      }
    }
  }
  
  /// فحص وضع التطوير على iOS
  static Future<bool> _checkIOSDeveloperMode() async {
    try {
      final bool isDeveloperMode = await _channel.invokeMethod('isDeveloperModeEnabled') ?? false;
      return isDeveloperMode;
    } on PlatformException catch (e) {
      debugPrint('خطأ في فحص وضع التطوير على iOS: ${e.message}');
      return false;
    }
  }
  
  /// الحصول على معلومات إضافية حول حالة وضع المطور
  static Future<Map<String, dynamic>> getDeveloperModeDetails() async {
    try {
      if (!_shouldCheckDeveloperMode) {
        return {
          'isDeveloperMode': false,
          'reason': 'Debug mode - Developer mode check disabled',
          'details': {},
        };
      }
      
      if (Platform.isAndroid) {
        final Map<dynamic, dynamic> details = await _channel.invokeMethod('getDeveloperModeDetails') ?? {};
        return Map<String, dynamic>.from(details);
      } else if (Platform.isIOS) {
        final Map<dynamic, dynamic> details = await _channel.invokeMethod('getDeveloperModeDetails') ?? {};
        return Map<String, dynamic>.from(details);
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على تفاصيل وضع المطور: $e');
    }
    
    return {
      'isDeveloperMode': false,
      'reason': 'Unknown platform or error occurred',
      'details': {},
    };
  }
  
  /// التحقق من إعدادات محددة تشير إلى وضع المطور
  static Future<bool> checkSpecificDeveloperSettings() async {
    try {
      if (!_shouldCheckDeveloperMode) {
        return false;
      }
      
      if (Platform.isAndroid) {
        // فحص إعدادات محددة على Android
        final bool hasDevSettings = await _channel.invokeMethod('checkSpecificSettings') ?? false;
        return hasDevSettings;
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص الإعدادات المحددة: $e');
    }
    
    return false;
  }
  
  /// متغيرات للحالة الحالية
  static bool _isMonitoring = false;
  static bool _lastKnownState = false;
  
  /// بدء المراقبة المستمرة لوضع المطور
  static void startContinuousMonitoring() {
    if (_isMonitoring || !_shouldCheckDeveloperMode) {
      return;
    }
    
    _isMonitoring = true;
    debugPrint('🔍 بدء المراقبة المستمرة لوضع المطور');
    
    startDeveloperModeMonitoring().listen((bool isDeveloperMode) {
      if (isDeveloperMode != _lastKnownState) {
        _lastKnownState = isDeveloperMode;
        
        if (isDeveloperMode) {
          debugPrint('🚨 تغيير الحالة: تم تفعيل وضع المطور!');
        } else {
          debugPrint('✅ تغيير الحالة: تم إيقاف وضع المطور');
        }
      }
    });
  }
  
  /// إيقاف المراقبة المستمرة
  static void stopContinuousMonitoring() {
    _isMonitoring = false;
    debugPrint('⏹️ تم إيقاف مراقبة وضع المطور');
  }
  
  /// الحصول على الحالة الأخيرة المعروفة
  static bool get lastKnownDeveloperModeState => _lastKnownState;
  
  /// التحقق من أن المراقبة مفعلة
  static bool get isMonitoring => _isMonitoring;
  
  /// فحص سريع بدون استدعاء Native code (للاستخدام في الواجهة)
  static bool get shouldShowDeveloperModeWarning {
    return _shouldCheckDeveloperMode && _lastKnownState;
  }
} 