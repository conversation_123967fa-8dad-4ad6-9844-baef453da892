# 🔔 نظام الإشعارات - دليل سريع

## ✅ تم الإعداد بنجاح!

تم إعداد نظام الإشعارات بالكامل في التطبيق باستخدام Firebase Cloud Messaging.

## 🚀 الاستخدام السريع

### 1. إرسال إشعار محلي
```dart
import 'package:webinar/services/notification_service.dart';

final notificationService = NotificationService();

await notificationService.sendLocalNotification(
  title: 'عنوان الإشعار',
  body: 'محتوى الإشعار',
  data: {'type': 'course', 'id': '123'},
);
```

### 2. جدولة إشعار
```dart
await notificationService.scheduleLocalNotification(
  title: 'إشعار مجدول',
  body: 'سيظهر بعد ساعة',
  scheduledDate: DateTime.now().add(Duration(hours: 1)),
);
```

### 3. الحصول على FCM Token
```dart
String? token = await notificationService.getToken();
print('FCM Token: $token');
```

## 📱 اختبار النظام

1. شغل التطبيق على جهاز حقيقي
2. ستظهر رسالة طلب الأذونات
3. اقبل الأذونات
4. ستحصل على FCM Token في الكونسول

## 🔧 ملفات مهمة

- `lib/config/notification.dart` - إعدادات الإشعارات
- `lib/services/notification_service.dart` - خدمة الإشعارات
- `lib/examples/notification_example.dart` - صفحة اختبار
- `android/app/google-services.json` - إعدادات Firebase

## 🎯 أنواع الإشعارات المدعومة

| النوع | المفتاح | الوصف |
|-------|---------|--------|
| كورس | `course` | إشعارات الكورسات |
| مهمة | `assignment` | إشعارات المهام |
| اختبار | `quiz` | إشعارات الاختبارات |
| اجتماع | `meeting` | إشعارات الاجتماعات |
| رسالة | `message` | إشعارات الرسائل |

## 🌐 إرسال من الخادم

```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "FCM_TOKEN",
    "notification": {
      "title": "عنوان الإشعار",
      "body": "محتوى الإشعار"
    },
    "data": {
      "type": "course",
      "course_id": "123"
    }
  }'
```

## 🔍 استكشاف الأخطاء

- **الإشعارات لا تظهر**: تحقق من الأذونات
- **FCM Token فارغ**: تحقق من الاتصال بالإنترنت
- **التنقل لا يعمل**: تحقق من أسماء الصفحات

## 📞 الدعم

راجع الملف الكامل: `NOTIFICATION_SETUP_GUIDE.md`

---
✨ **النظام جاهز للاستخدام!** ✨ 