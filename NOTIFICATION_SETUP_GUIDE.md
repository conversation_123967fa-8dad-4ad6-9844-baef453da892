# دليل إعداد الإشعارات - Firebase Cloud Messaging

## نظرة عامة
تم إعداد نظام الإشعارات في التطبيق باستخدام Firebase Cloud Messaging (FCM) مع الإشعارات المحلية. يدعم النظام:

- ✅ الإشعارات من الخادم (Firebase)
- ✅ الإشعارات المحلية
- ✅ الإشعارات المجدولة
- ✅ التنقل التلقائي حسب نوع الإشعار
- ✅ إدارة المواضيع (Topics)
- ✅ معالجة الإشعارات في جميع حالات التطبيق

## الملفات المهمة

### 1. ملفات الإعداد
- `lib/config/notification.dart` - إعدادات الإشعارات الأساسية
- `lib/services/notification_service.dart` - خدمة إدارة الإشعارات
- `lib/main.dart` - تهيئة الإشعارات في التطبيق
- `android/app/google-services.json` - إعدادات Firebase

### 2. ملفات Android
- `android/app/build.gradle` - إعدادات Firebase للأندرويد
- `android/build.gradle` - إضافة Google Services plugin
- `android/app/src/main/AndroidManifest.xml` - أذونات الإشعارات

## كيفية الاستخدام

### 1. تهيئة الخدمة
```dart
import 'package:webinar/services/notification_service.dart';

// في main.dart أو في أي مكان تريد تهيئة الخدمة
final NotificationService notificationService = NotificationService();
await notificationService.initialize();
```

### 2. إرسال إشعار محلي
```dart
await notificationService.sendLocalNotification(
  title: 'عنوان الإشعار',
  body: 'محتوى الإشعار',
  data: {
    'type': 'course',
    'course_id': '123',
  },
);
```

### 3. جدولة إشعار
```dart
DateTime scheduledDate = DateTime.now().add(Duration(hours: 1));

await notificationService.scheduleLocalNotification(
  title: 'إشعار مجدول',
  body: 'هذا إشعار تم جدولته',
  scheduledDate: scheduledDate,
);
```

### 4. إدارة المواضيع
```dart
// الاشتراك في موضوع
await notificationService.subscribe('general');

// إلغاء الاشتراك
await notificationService.unsubscribe('general');
```

### 5. الحصول على FCM Token
```dart
String? token = await notificationService.getToken();
print('FCM Token: $token');
```

## أنواع الإشعارات المدعومة

### 1. إشعارات الكورسات
```json
{
  "type": "course",
  "course_id": "123",
  "course_name": "اسم الكورس"
}
```

### 2. إشعارات المهام
```json
{
  "type": "assignment",
  "assignment_id": "456",
  "assignment_name": "اسم المهمة"
}
```

### 3. إشعارات الاختبارات
```json
{
  "type": "quiz",
  "quiz_id": "789",
  "quiz_name": "اسم الاختبار"
}
```

### 4. إشعارات الاجتماعات
```json
{
  "type": "meeting",
  "meeting_id": "101",
  "meeting_name": "اسم الاجتماع"
}
```

### 5. إشعارات الرسائل
```json
{
  "type": "message",
  "message_id": "202"
}
```

## إرسال الإشعارات من الخادم

### 1. باستخدام Firebase Console
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى Cloud Messaging
4. انقر على "Send your first message"
5. املأ البيانات وأرسل

### 2. باستخدام REST API
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "FCM_TOKEN_HERE",
    "notification": {
      "title": "عنوان الإشعار",
      "body": "محتوى الإشعار"
    },
    "data": {
      "type": "course",
      "course_id": "123"
    }
  }'
```

### 3. إرسال لموضوع معين
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "/topics/general",
    "notification": {
      "title": "إشعار عام",
      "body": "هذا إشعار لجميع المشتركين"
    }
  }'
```

## اختبار الإشعارات

### 1. استخدام صفحة الاختبار
تم إنشاء صفحة اختبار في `lib/examples/notification_example.dart` تحتوي على:
- عرض FCM Token
- إرسال إشعارات تجريبية
- اختبار أنواع مختلفة من الإشعارات
- إدارة المواضيع

### 2. اختبار الإشعارات المحلية
```dart
// في أي مكان في التطبيق
import 'package:webinar/services/notification_service.dart';

final notificationService = NotificationService();

// إشعار فوري
await notificationService.sendLocalNotification(
  title: 'اختبار',
  body: 'هذا اختبار للإشعارات',
);
```

## استكشاف الأخطاء

### 1. الإشعارات لا تظهر
- تأكد من أن الأذونات ممنوحة
- تحقق من إعدادات الجهاز
- تأكد من أن التطبيق ليس في وضع "عدم الإزعاج"

### 2. FCM Token فارغ
- تأكد من إعداد Firebase بشكل صحيح
- تحقق من ملف `google-services.json`
- تأكد من الاتصال بالإنترنت

### 3. الإشعارات لا تعمل في الخلفية
- تأكد من إعداد `onBackgroundMessage`
- تحقق من أذونات الخلفية في الجهاز

### 4. التنقل لا يعمل
- تأكد من أن `navigatorKey` مُعرف بشكل صحيح
- تحقق من أسماء الصفحات في التطبيق

## الأذونات المطلوبة

### Android
```xml
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
<uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

### iOS
سيتم طلب الأذونات تلقائياً عند تشغيل التطبيق.

## نصائح مهمة

1. **احفظ FCM Token**: احفظ التوكن في قاعدة البيانات لإرسال إشعارات مخصصة
2. **استخدم المواضيع**: للإشعارات العامة، استخدم المواضيع بدلاً من الإرسال الفردي
3. **اختبر على أجهزة حقيقية**: المحاكيات قد لا تدعم جميع ميزات الإشعارات
4. **راقب الأداء**: الإشعارات الكثيرة قد تؤثر على أداء التطبيق

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من وحدة التحكم (Console) للأخطاء
2. راجع [وثائق Firebase](https://firebase.google.com/docs/cloud-messaging)
3. تأكد من أن جميع التبعيات محدثة

---

تم إعداد النظام بشكل كامل وجاهز للاستخدام! 🎉 