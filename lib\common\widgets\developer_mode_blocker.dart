import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/services/developer_mode_service.dart';

/// Widget لمنع استخدام التطبيق عند تفعيل وضع المطور
/// Developer Mode Blocker - يظهر شاشة كاملة لا يمكن تجاوزها
class DeveloperModeBlocker extends StatefulWidget {
  final Widget child;
  
  const DeveloperModeBlocker({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<DeveloperModeBlocker> createState() => _DeveloperModeBlockerState();
}

class _DeveloperModeBlockerState extends State<DeveloperModeBlocker>
    with TickerProviderStateMixin {
  
  bool _isDeveloperModeEnabled = false;
  bool _isChecking = true;
  late AnimationController _pulseController;
  late AnimationController _shakeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shakeAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // إعداد الأنيميشن
    _setupAnimations();
    
    // فحص وضع المطور عند البدء
    _checkDeveloperMode();
    
    // بدء المراقبة المستمرة
    _startContinuousMonitoring();
  }
  
  void _setupAnimations() {
    // أنيميشن النبض للأيقونة
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // أنيميشن الاهتزاز للتحذير
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(
      begin: -5.0,
      end: 5.0,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
    
    // تشغيل الأنيميشن بشكل متكرر
    _pulseController.repeat(reverse: true);
  }
  
  Future<void> _checkDeveloperMode() async {
    try {
      final bool isDeveloperMode = await DeveloperModeService.isDeveloperModeEnabled();
      
      if (mounted) {
        setState(() {
          _isDeveloperModeEnabled = isDeveloperMode;
          _isChecking = false;
        });
        
        // إذا تم اكتشاف وضع المطور، اهتز
        if (isDeveloperMode) {
          _shakeController.forward().then((_) {
            _shakeController.reverse();
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في فحص وضع المطور: $e');
      if (mounted) {
        setState(() {
          _isDeveloperModeEnabled = false;
          _isChecking = false;
        });
      }
    }
  }
  
  void _startContinuousMonitoring() {
    DeveloperModeService.startDeveloperModeMonitoring().listen((bool isDeveloperMode) {
      if (mounted && isDeveloperMode != _isDeveloperModeEnabled) {
        setState(() {
          _isDeveloperModeEnabled = isDeveloperMode;
        });
        
        // إذا تم تفعيل وضع المطور، اهتز
        if (isDeveloperMode) {
          _shakeController.forward().then((_) {
            _shakeController.reverse();
          });
        }
      }
    });
  }
  
  @override
  void dispose() {
    _pulseController.dispose();
    _shakeController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // إذا كنا نفحص أو وضع المطور غير مفعل، اعرض التطبيق العادي
    if (_isChecking || !_isDeveloperModeEnabled) {
      return widget.child;
    }
    
    // إذا كان وضع المطور مفعلاً، اعرض شاشة المنع
    return _buildDeveloperModeBlockerScreen();
  }
  
  /// بناء شاشة منع استخدام التطبيق
  Widget _buildDeveloperModeBlockerScreen() {
    return PopScope(
      canPop: false, // منع الرجوع للخلف
      child: Scaffold(
        backgroundColor: const Color(0xFF1a1a1a), // خلفية داكنة
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xFF2d1b69), // بنفسجي داكن
                const Color(0xFF1a1a1a), // أسود
                const Color(0xFF4a1a1a), // أحمر داكن
              ],
            ),
          ),
          child: SafeArea(
            child: AnimatedBuilder(
              animation: _shakeAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(_shakeAnimation.value, 0),
                  child: _buildBlockerContent(),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
  
  /// محتوى شاشة المنع
  Widget _buildBlockerContent() {
    return Padding(
      padding: padding(horizontal: 30, vertical: 40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          
          // أيقونة التحذير المتحركة
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  padding: padding(all: 30),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: red49.withOpacity(0.2),
                    border: Border.all(
                      color: red49,
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: red49.withOpacity(0.5),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.warning_rounded,
                    size: 80,
                    color: red49,
                  ),
                ),
              );
            },
          ),
          
          space(40),
          
          // عنوان التحذير
          Text(
            '⚠️ تنبيه أمني',
            style: style24Bold().copyWith(
              color: Colors.white,
              fontSize: 32,
              shadows: [
                Shadow(
                  color: red49.withOpacity(0.5),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          
          space(20),
          
          // وصف المشكلة
          Container(
            padding: padding(all: 25),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.05),
              borderRadius: borderRadius(radius: 20),
              border: Border.all(
                color: red49.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'تم اكتشاف تفعيل وضع المطوّر في جهازك',
                  style: style18Bold().copyWith(
                    color: Colors.white,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                space(15),
                
                Text(
                  'لأسباب أمنية وحماية المحتوى التعليمي، يُرجى إيقاف وضع المطوّر لاستخدام هذا التطبيق.',
                  style: style14Regular().copyWith(
                    color: Colors.white.withOpacity(0.8),
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          space(30),
          
          // خطوات الحل
          _buildSolutionSteps(),
          
          const Spacer(),
          
          // معلومات إضافية
          Container(
            padding: padding(horizontal: 20, vertical: 15),
            decoration: BoxDecoration(
              color: green77().withOpacity(0.1),
              borderRadius: borderRadius(radius: 15),
              border: Border.all(
                color: green77().withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: green77(),
                  size: 24,
                ),
                space(0, width: 12),
                Expanded(
                  child: Text(
                    'سيعود التطبيق للعمل تلقائياً بعد إيقاف وضع المطوّر',
                    style: style12Regular().copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          space(20),
          
          // شعار التطبيق
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: padding(all: 8),
                decoration: BoxDecoration(
                  color: green77().withOpacity(0.2),
                  borderRadius: borderRadius(radius: 10),
                ),
                child: SvgPicture.asset(
                  AppAssets.logoSvg,
                  width: 30,
                  height: 30,
                  colorFilter: ColorFilter.mode(green77(), BlendMode.srcIn),
                ),
              ),
              space(0, width: 12),
              Text(
                'منصة السلطان التعليمية',
                style: style14Bold().copyWith(
                  color: green77(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// بناء خطوات الحل
  Widget _buildSolutionSteps() {
    final steps = [
      {
        'number': '1',
        'title': 'افتح إعدادات الجهاز',
        'icon': Icons.settings,
      },
      {
        'number': '2', 
        'title': 'انتقل إلى "النظام" ← "خيارات المطوّر"',
        'icon': Icons.developer_mode,
      },
      {
        'number': '3',
        'title': 'قم بإيقاف "خيارات المطوّر"',
        'icon': Icons.toggle_off,
      },
      {
        'number': '4',
        'title': 'اعد فتح التطبيق',
        'icon': Icons.refresh,
      },
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'خطوات الحل:',
          style: style16Bold().copyWith(
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        
        space(15),
        
        ...steps.map((step) => _buildSolutionStep(
          step['number'] as String,
          step['title'] as String,
          step['icon'] as IconData,
        )).toList(),
      ],
    );
  }
  
  /// بناء خطوة واحدة من خطوات الحل
  Widget _buildSolutionStep(String number, String title, IconData icon) {
    return Container(
      margin: padding(vertical: 6),
      padding: padding(all: 15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: borderRadius(radius: 12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // رقم الخطوة
          Container(
            width: 35,
            height: 35,
            decoration: BoxDecoration(
              color: green77(),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: green77().withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Text(
                number,
                style: style14Bold().copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          
          space(0, width: 15),
          
          // أيقونة الخطوة
          Container(
            padding: padding(all: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: borderRadius(radius: 8),
            ),
            child: Icon(
              icon,
              color: Colors.white.withOpacity(0.8),
              size: 20,
            ),
          ),
          
          space(0, width: 15),
          
          // عنوان الخطوة
          Expanded(
            child: Text(
              title,
              style: style12Regular().copyWith(
                color: Colors.white.withOpacity(0.9),
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }
} 