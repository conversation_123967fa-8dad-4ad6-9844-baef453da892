import 'package:flutter/material.dart';
import 'package:webinar/services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🚀 بدء اختبار نظام الإشعارات...');
  
  try {
    // تهيئة خدمة الإشعارات
    final NotificationService notificationService = NotificationService();
    await notificationService.initialize();
    print('✅ تم تهيئة خدمة الإشعارات بنجاح');
    
    // الحصول على FCM Token
    String? token = await notificationService.getToken();
    if (token != null) {
      print('✅ تم الحصول على FCM Token: ${token.substring(0, 20)}...');
    } else {
      print('⚠️ لم يتم الحصول على FCM Token');
    }
    
    // اختبار إرسال إشعار محلي
    await notificationService.sendLocalNotification(
      title: 'اختبار النظام',
      body: 'تم إعداد نظام الإشعارات بنجاح! 🎉',
      data: {
        'type': 'test',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    print('✅ تم إرسال إشعار اختبار محلي');
    
    // اختبار الاشتراك في موضوع
    await notificationService.subscribe('test_topic');
    print('✅ تم الاشتراك في موضوع الاختبار');
    
    print('🎉 تم اختبار جميع وظائف الإشعارات بنجاح!');
    print('📱 يمكنك الآن استخدام النظام في التطبيق');
    
  } catch (e) {
    print('❌ خطأ في اختبار الإشعارات: $e');
  }
} 