import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webinar/app/models/library_model.dart';
import 'package:webinar/app/providers/app_language_provider.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/components.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';
import 'package:webinar/locator.dart';
import 'package:webinar/common/enums/error_enum.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../common/utils/object_instance.dart';

class LibrariesPage extends StatefulWidget {
  static const String pageName = '/libraries';
  const LibrariesPage({super.key});

  @override
  State<LibrariesPage> createState() => _LibrariesPageState();
}

class _LibrariesPageState extends State<LibrariesPage> {
  String selectedGovernorate = '';
  bool isLoading = false;

  // Sample data - في التطبيق الحقيقي ستأتي من API
  final Map<String, List<LibraryModel>> librariesByGovernorate = {
    'رصافة': [
      LibraryModel(
        id: 1,
        name: 'مكتبة المستنصرية',
        phone: '07733334104',
        address: 'شارع فلسطين - رصافة - بغداد',
        governorate: 'رصافة',
        latitude: 33.3419,
        longitude: 44.4009,
      ),
    ],
    'كرخ': [
      LibraryModel(
        id: 2,
        name: 'مكتبة الجوهرة (البتول)',
        phone: '07702538881',
        address: 'شارع الكرادة - كرخ - بغداد',
        governorate: 'كرخ',
        latitude: 33.3065,
        longitude: 44.3889,
      ),
      LibraryModel(
        id: 3,
        name: 'مكتبة باركود',
        phone: '07733553500',
        address: 'المنصور - كرخ - بغداد',
        governorate: 'كرخ',
        latitude: 33.2854,
        longitude: 44.3015,
      ),
      LibraryModel(
        id: 4,
        name: 'مكتبة مارينا',
        phone: '07713332255',
        address: 'الجادرية - كرخ - بغداد',
        governorate: 'كرخ',
        latitude: 33.2778,
        longitude: 44.3661,
      ),
      LibraryModel(
        id: 5,
        name: 'المتاسنر 2',
        phone: '07802855333',
        address: 'الكاظمية - كرخ - بغداد',
        governorate: 'كرخ',
        latitude: 33.3794,
        longitude: 44.3408,
      ),
      LibraryModel(
        id: 6,
        name: 'مكتبة الاحسان',
        phone: '07726556414',
        address: 'العدل - كرخ - بغداد',
        governorate: 'كرخ',
        latitude: 33.3152,
        longitude: 44.3661,
      ),
      LibraryModel(
        id: 7,
        name: 'مكتبة الربيعي',
        phone: '07703331873',
        address: 'حي الشرطة - كرخ - بغداد',
        governorate: 'كرخ',
        latitude: 33.2675,
        longitude: 44.2975,
      ),
    ],
    'محافظات': [
      LibraryModel(
        id: 8,
        name: 'الجذور - البصرة',
        phone: '07702687911',
        address: 'وسط البصرة - محافظة البصرة',
        governorate: 'محافظات',
        latitude: 30.5084,
        longitude: 47.7804,
      ),
      LibraryModel(
        id: 9,
        name: 'الذهبي - ديالى',
        phone: '07702406444',
        address: 'مركز بعقوبة - محافظة ديالى',
        governorate: 'محافظات',
        latitude: 33.7500,
        longitude: 44.6333,
      ),
      LibraryModel(
        id: 10,
        name: 'التاج - بابل',
        phone: '07802767474',
        address: 'مركز الحلة - محافظة بابل',
        governorate: 'محافظات',
        latitude: 32.5407,
        longitude: 44.4240,
      ),
      LibraryModel(
        id: 11,
        name: 'الصديقين - صلاح الدين',
        phone: '07702854488',
        address: 'مركز تكريت - محافظة صلاح الدين',
        governorate: 'محافظات',
        latitude: 34.5975,
        longitude: 43.6783,
      ),
      LibraryModel(
        id: 12,
        name: 'اشرف وخلدون - ميسان',
        phone: '07705572853',
        address: 'مركز العمارة - محافظة ميسان',
        governorate: 'محافظات',
        latitude: 31.8357,
        longitude: 47.1397,
      ),
      LibraryModel(
        id: 13,
        name: 'كنانة - موصل',
        phone: '07821654789',
        address: 'مركز الموصل - محافظة نينوى',
        governorate: 'محافظات',
        latitude: 36.3400,
        longitude: 43.1300,
      ),
    ],
  };

  List<String> get governorates => librariesByGovernorate.keys.toList();

  @override
  Widget build(BuildContext context) {
    return Consumer<AppLanguageProvider>(
      builder: (context, appLanguageProvider, _) {
        return directionality(
          child: Scaffold(
            backgroundColor: greyFA,
            appBar: appbar(
              title: appText.libraries,
            ),
            body: Column(
              children: [
                // Header section
                Container(
                  width: getSize().width,
                  padding: padding(all: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            color: green77(),
                            size: 24,
                          ),
                          space(0, width: 8),
                          Text(
                            appText.selectByGovernorate,
                            style: style16Bold(),
                          ),
                        ],
                      ),
                      space(8),
                      Text(
                        'اختر المحافظة لعرض المكتبات المتوفرة',
                        style: style14Regular().copyWith(color: greyB2),
                      ),
                    ],
                  ),
                ),

                // Governorates list
                Expanded(
                  child: ListView.builder(
                    padding: padding(horizontal: 16, vertical: 16),
                    itemCount: governorates.length,
                    itemBuilder: (context, index) {
                      final governorate = governorates[index];
                      final libraries = librariesByGovernorate[governorate] ?? [];
                      final isExpanded = selectedGovernorate == governorate;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: borderRadius(radius: 12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Governorate header
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedGovernorate = isExpanded ? '' : governorate;
                                });
                              },
                              behavior: HitTestBehavior.opaque,
                              child: Container(
                                width: getSize().width,
                                padding: padding(all: 16),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.location_on,
                                      color: green77(),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        governorate,
                                        style: style16Bold(),
                                      ),
                                    ),
                                    Container(
                                      padding: padding(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: green77().withOpacity(0.1),
                                        borderRadius: borderRadius(radius: 12),
                                      ),
                                      child: Text(
                                        '${libraries.length} ${appText.librariesAvailable}',
                                        style: style12Regular().copyWith(
                                          color: green77(),
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    AnimatedRotation(
                                      turns: isExpanded ? 0.5 : 0,
                                      duration: const Duration(milliseconds: 300),
                                      child: Icon(
                                        Icons.keyboard_arrow_down,
                                        color: greyB2,
                                        size: 24,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            // Libraries list
                            if (isExpanded)
                              ...libraries.map((library) => _buildLibraryItem(library)).toList(),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLibraryItem(LibraryModel library) {
    return Container(
      padding: padding(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: greyE7, width: 1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Library info and phone in one row
          Row(
            children: [
              // Library info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      library.name,
                      style: style14Bold(),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      library.address,
                      style: style12Regular().copyWith(color: greyB2),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // Phone number button
              GestureDetector(
                onTap: () {
                  _makePhoneCall(library.phone);
                },
                behavior: HitTestBehavior.opaque,
                child: Container(
                  padding: padding(horizontal: 8, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: borderRadius(radius: 8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.phone,
                        color: Colors.blue.shade700,
                        size: 16,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        library.phone,
                        style: style10Regular().copyWith(
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Map button - full width
          GestureDetector(
            onTap: () {
              _openMap(library);
            },
            behavior: HitTestBehavior.opaque,
            child: Container(
              width: double.infinity,
              padding: padding(vertical: 10),
              decoration: BoxDecoration(
                color: green77(),
                borderRadius: borderRadius(radius: 8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'عرض الموقع',
                    style: style14Regular().copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openMap(LibraryModel library) async {
    final url = 'https://www.google.com/maps/search/?api=1&query=${library.latitude},${library.longitude}';
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        showSnackBar(ErrorEnum.error, 'لا يمكن فتح تطبيق الخرائط');
      }
    } catch (e) {
      showSnackBar(ErrorEnum.error, 'حدث خطأ في فتح الخرائط');
    }
  }

  void _makePhoneCall(String phoneNumber) async {
    final url = 'tel:$phoneNumber';
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      } else {
        showSnackBar(ErrorEnum.error, 'لا يمكن إجراء المكالمة');
      }
    } catch (e) {
      showSnackBar(ErrorEnum.error, 'حدث خطأ في الاتصال');
    }
  }
} 