import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webinar/app/providers/app_language_provider.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/components.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';
import 'package:webinar/locator.dart';

class SubscriptionGuidePage extends StatefulWidget {
  static const String pageName = '/subscription-guide';
  const SubscriptionGuidePage({super.key});

  @override
  State<SubscriptionGuidePage> createState() => _SubscriptionGuidePageState();
}

class _SubscriptionGuidePageState extends State<SubscriptionGuidePage> {
  // Define the purple color
  final Color purpleColor = const Color(0xFF754FFE);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppLanguageProvider>(
      builder: (context, appLanguageProvider, _) {
        return directionality(
          child: Scaffold(
            backgroundColor: greyFA,
            appBar: appbar(
              title: 'شرح كيفية الاشتراك',
            ),
            body: SingleChildScrollView(
              padding: padding(horizontal: 20, vertical: 30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header illustration
                  Container(
                    width: getSize().width,
                    height: 200,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          purpleColor.withOpacity(0.3),
                          purpleColor.withOpacity(0.1),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: borderRadius(radius: 20),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.school,
                            size: 60,
                            color: purpleColor,
                          ),
                          space(12),
                          Text(
                            'كيفية الاشتراك',
                            style: style20Bold().copyWith(
                              color: purpleColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  space(30),

                  // Steps
                  _buildStepCard(
                    '1',
                    'زيارة المكتبة',
                    'توجه إلى أقرب مكتبة من المكتبات المشاركة في برنامج "منصة السلطان"',
                    Icons.location_on,
                    Colors.orange,
                  ),

                  space(20),

                  _buildStepCard(
                    '2',
                    'شراء المفتاح',
                    'اطلب من المكتبة شراء مفتاح اشتراك لمنصة السلطان التعليمية',
                    Icons.shopping_cart,
                    Colors.blue,
                  ),

                  space(20),

                  _buildStepCard(
                    '3',
                    'الحصول على المفتاح',
                    'ستحصل على مفتاح مكون من أرقام وحروف، احتفظ به في مكان آمن',
                    Icons.vpn_key,
                    purpleColor,
                  ),

                  space(20),

                  _buildStepCard(
                    '4',
                    'إدخال المفتاح',
                    'ارجع إلى التطبيق وأدخل المفتاح في الصفحة الرئيسية لتطبيق المفتاح',
                    Icons.login,
                    purpleColor,
                  ),

                  space(20),

                  _buildStepCard(
                    '5',
                    'الاستمتاع بالتعلم',
                    'بعد تفعيل المفتاح، يمكنك الوصول لجميع الدورات والمحتويات التعليمية',
                    Icons.celebration,
                    Colors.amber,
                  ),

                  space(30),

                  // Important notes
                  Container(
                    width: getSize().width,
                    padding: padding(horizontal: 20, vertical: 20),
                    decoration: BoxDecoration(
                      color: purpleColor.withOpacity(0.1),
                      borderRadius: borderRadius(radius: 16),
                      border: Border.all(color: purpleColor.withOpacity(0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info,
                              color: purpleColor,
                              size: 20,
                            ),
                            space(0, width: 8),
                            Text(
                              'ملاحظات مهمة:',
                              style: style16Bold().copyWith(
                                color: purpleColor,
                              ),
                            ),
                          ],
                        ),

                        space(12),

                        _buildNote('• كل مفتاح صالح لاستخدام واحد فقط'),
                        _buildNote('• احتفظ بالمفتاح في مكان آمن'),
                        _buildNote('• لا تشارك المفتاح مع أي شخص آخر'),
                        _buildNote('• في حالة فقدان المفتاح، توجه للمكتبة مرة أخرى'),
                      ],
                    ),
                  ),

                  space(30),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStepCard(String stepNumber, String title, String description, IconData icon, Color color) {
    return Container(
      width: getSize().width,
      padding: padding(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius(radius: 16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Step number circle
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                stepNumber,
                style: style18Bold().copyWith(
                  color: color,
                ),
              ),
            ),
          ),

          space(0, width: 16),

          // Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: borderRadius(radius: 8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),

          space(0, width: 16),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: style16Bold().copyWith(
                    color: Colors.black87,
                  ),
                ),
                space(4),
                Text(
                  description,
                  style: style14Regular().copyWith(
                    color: greyB2,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNote(String text) {
    return Padding(
      padding: padding(vertical: 2),
      child: Text(
        text,
        style: style14Regular().copyWith(
          color: purpleColor.withOpacity(0.8),
        ),
      ),
    );
  }
} 