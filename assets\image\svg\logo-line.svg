<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 213 213">
  <defs>
    <style>
      .cls-1 {
        stroke: #fff;
        stroke-dasharray: 5 5;
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5 {
        fill: #fff;
      }

      .cls-2 {
        opacity: 1;
      }

      .cls-2, .cls-3, .cls-4 {
        fill-rule: evenodd;
        isolation: isolate;
      }

      .cls-3 {
        opacity: 1;
      }

      .cls-4 {
        opacity: 1;
      }

      .cls-6 {
        fill: #f7931e;
      }

      .cls-7 {
        fill: #5d3891;
      }

      .cls-8 {
        fill: #f99417;
      }

      .cls-9 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="cls-5" y="0" width="213" height="213"/>
    </clipPath>
  </defs>
  <!-- Generator: Adobe Illustrator 28.6.0, SVG Export Plug-In . SVG Version: 1.2.0 Build 709)  -->
  <g>
    <g id="Layer_1">
      <g>
        <rect class="cls-5" y="0" width="213" height="213"/>
        <g class="cls-9">
          <g>
            <path class="cls-5" d="M106.5,197c50,0,90.5-40.5,90.5-90.5S156.5,16,106.5,16,16,56.5,16,106.5s40.5,90.5,90.5,90.5Z"/>
            <path class="cls-1" d="M106.5,212.5c58.5,0,106-47.5,106-106S165,.5,106.5.5.5,48,.5,106.5s47.5,106,106,106Z"/>
            <path class="cls-2" d="M105.6,67.6c2.2-.2,4.4.2,6.4,1.1,8.6,5,17.2,10,25.9,15,2.8,2.2,4.4,5.1,4.7,8.7,0,9.7,0,19.5,0,29.2-.4,3.9-2.1,7-5.4,9.1-8.1,4.7-16.3,9.4-24.4,14.1-3.1,1.6-6.3,2-9.6,1-.5-.2-1.1-.4-1.6-.6-8.6-4.9-17.1-9.9-25.7-14.9-2.8-2.2-4.4-5.1-4.7-8.7,0-9.7,0-19.5,0-29.2.4-4.1,2.3-7.2,5.7-9.4,8.3-4.7,16.6-9.5,24.8-14.3,1.2-.5,2.5-.9,3.8-1.1ZM106.2,72c1.5-.1,2.9.2,4.2.9,8.1,4.7,16.2,9.4,24.4,14.1,1.9,1.3,3,3,3.4,5.2,0,9.9,0,19.8,0,29.6-.4,2.2-1.5,4-3.4,5.2-8.1,4.7-16.3,9.4-24.4,14.1-2.3,1.1-4.6,1.1-7,0-8.1-4.7-16.2-9.4-24.4-14.1-1.9-1.3-3-3-3.4-5.2,0-9.9,0-19.8,0-29.6.4-2.4,1.7-4.2,3.7-5.4,8-4.6,16-9.2,24-13.9.9-.4,1.8-.7,2.7-.9Z"/>
            <path class="cls-4" d="M106.1,79.5c.4,0,.8,0,1.1.2,1.9,1.8,3.6,3.8,5,5.9,2,3.4,2.6,7,1.7,10.8,0,0,0,.1,0,.2.7,1,1.2,2.1,1.5,3.3.4,1.6.7,3.3.7,5-1.7-.2-3.5-.4-5.2-.7-.5.8-1,1.5-1.5,2.3-.2.1-.4.2-.6.2-1.6,0-3.3,0-4.9,0-.2,0-.4-.1-.6-.2-.6-.7-1.1-1.5-1.5-2.3-.4.1-.8.2-1.2.3-1.3.2-2.6.3-4,.5,0-2.2.5-4.4,1.3-6.5.3-.7.7-1.3,1-1.9-1-4.6,0-8.8,2.9-12.4,1.2-1.5,2.5-2.9,3.8-4.2.1-.1.3-.2.4-.3Z"/>
            <path class="cls-3" d="M105,107.7h3.3c-.1,1.5,0,2.9,0,4.4.5,3.8,2.6,5.8,6.4,5.9,4.9-.4,9.8-1.3,14.4-2.9,2.6-.9,5.1-2.2,7.3-3.9,0,3.4,0,6.7,0,10.1-.2,1.8-1,3.3-2.5,4.3-8.1,4.7-16.3,9.4-24.4,14.1-1.4.7-2.9.8-4.4.4-.4-.1-.8-.3-1.1-.4-8.1-4.7-16.2-9.4-24.4-14.1-1.2-.9-2-2.1-2.4-3.7-.1-3.5-.1-7-.1-10.5,4.1,2.5,8.6,4.3,13.3,5.3,2.5.6,5,1,7.6,1.3,2.9.3,4.9-.8,6.1-3.4.6-1.5.8-3.1.8-4.7,0-.7,0-1.4,0-2.1Z"/>
          </g>
        </g>
      </g>
      <g id="b">
        <path class="cls-8" d="M170,83.4h7.7v46.2h-7.7v-46.2Z"/>
        <path class="cls-8" d="M62.2,112.3v17.3h-26.9v-17.3h7.7v9.6h11.5v-9.6h7.7Z"/>
        <path class="cls-7" d="M62.2,83.4v27h-7.7v-19.3h-1.9v19.3h-7.7v-19.3h-1.9v19.3h-7.7v-27h26.9,0Z"/>
        <path class="cls-8" d="M44.9,112.3h7.7v7.7h-7.7v-7.7Z"/>
        <path class="cls-7" d="M141.1,83.4v27h7.7v-9.6h1.9v19.3h7.7v-36.6h-17.3ZM150.8,93h-1.9v-1.9h1.9v1.9Z"/>
        <path class="cls-7" d="M93,83.4v27h7.7v-9.6h1.9v19.3h7.7v-36.6h-17.3ZM102.7,93h-1.9v-1.9h1.9v1.9Z"/>
        <path class="cls-7" d="M91.1,83.4v27h-7l-.7-.8-11.5-13.9v5.1h-7.7v-17.3h7.5l.2.2,7.3,8.8,2.2,2.7,2,2.4v-14.1h7.7,0Z"/>
        <path class="cls-7" d="M129.6,91.1v1.9h9.6v17.3h-17.3v-7.7h9.6v-1.9h-9.6v-17.3h17.3v7.7h-9.6Z"/>
        <path class="cls-8" d="M160.4,83.4v38.5h-11.5v-9.6h-7.7v9.6h-1.9v-9.6h-7.7v9.6h-1.9v-9.6h-7.7v9.6h-1.9v-38.5h-7.7v38.5h-11.6v-9.6h-19.2v-2.1l-6.2-7.5h-1.5v19.3h-1.9v-19.3h-7.7v27h103.9v-46.2h-7.7,0,0ZM93,121.9h-11.5v-1.9h11.5v1.9Z"/>
        <path class="cls-6" d="M81.5,83.4v8.8l-7.3-8.8h7.3Z"/>
      </g>
    </g>
  </g>
</svg>