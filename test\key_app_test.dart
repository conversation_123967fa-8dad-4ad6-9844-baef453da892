import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:webinar/app/pages/main_page/key_app_page/key_app_page.dart';

void main() {
  group('Key App Page Tests', () {
    testWidgets('Key App Page should display correctly', (WidgetTester tester) async {
      // Test that the Key App page can be created without errors
      await tester.pumpWidget(
        MaterialApp(
          home: const KeyAppPage(),
        ),
      );

      // Verify the widget builds without throwing
      expect(find.byType(KeyAppPage), findsOneWidget);
    });

    testWidgets('Key input field should accept text', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const KeyAppPage(),
        ),
      );

      // Find text field
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // Enter text in the field
      await tester.enterText(textField, 'TEST123KEY');
      expect(find.text('TEST123KEY'), findsOneWidget);
    });
  });
} 