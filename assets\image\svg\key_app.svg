<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <linearGradient id="linear-gradient" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#9effc1"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
  </defs>
  <g id="Key" transform="translate(0 0)">
    <!-- Key head (circular part) - smaller and moved down -->
    <circle cx="13" cy="26.5" r="8" fill="url(#linear-gradient)" stroke="none"/>
    <circle cx="13" cy="26.5" r="3.2" fill="none" stroke="#4B9460" stroke-width="1.5"/>
    
    <!-- Key shaft - smaller and moved down -->
    <rect x="21" y="25" width="9" height="3" rx="1.5" fill="url(#linear-gradient)"/>
    
    <!-- Key teeth - smaller and moved down -->
    <rect x="27" y="28" width="3" height="3" rx="0.6" fill="url(#linear-gradient)"/>
    <rect x="25" y="28" width="1.8" height="4.5" rx="0.4" fill="url(#linear-gradient)"/>
  </g>
</svg> 