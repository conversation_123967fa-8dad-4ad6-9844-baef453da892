import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:webinar/app/pages/main_page/categories_page/filter_category_page/filter_category_page.dart';
import 'package:webinar/app/providers/app_language_provider.dart';
import 'package:webinar/app/providers/drawer_provider.dart';
import 'package:webinar/app/services/guest_service/categories_service.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/data/app_language.dart';
import 'package:webinar/common/shimmer_component.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/common/utils/cache_manager.dart';
import 'package:webinar/common/utils/performance_utils.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';
import 'package:webinar/locator.dart';

import '../../../../common/utils/object_instance.dart';
import '../../../models/category_model.dart';
import '../../../../common/components.dart';
import 'package:webinar/common/optimized_shimmer_component.dart';

class CategoriesPage extends StatefulWidget {
  const CategoriesPage({super.key});

  @override
  State<CategoriesPage> createState() => _CategoriesPageState();
}

class _CategoriesPageState extends State<CategoriesPage> 
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {

  bool isLoading = false;
  bool isInitialLoad = true;
  List<CategoryModel> trendCategories = [];
  List<CategoryModel> categories = [];
  
  // Performance optimization: Keep state alive
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    
    // تحميل البيانات بطريقة محسنة
    _initializeData();
  }

  /// تهيئة البيانات مع فحص التخزين المؤقت أولاً
  Future<void> _initializeData() async {
    // فحص حالة التخزين المؤقت
    final cacheStatus = await CategoriesService.getCacheStatus();
    
    if (cacheStatus['categories'] == true && cacheStatus['trendCategories'] == true) {
      // البيانات موجودة في التخزين المؤقت - تحميل فوري
      await _loadFromCache();
    } else {
      // لا توجد بيانات محفوظة - عرض shimmer وتحميل من الخادم
      setState(() {
        isLoading = true;
        isInitialLoad = true;
      });
      await _loadFromServer();
    }
  }

  /// تحميل البيانات من التخزين المؤقت
  Future<void> _loadFromCache() async {
    try {
      // تحميل متوازي من التخزين المؤقت
      final results = await Future.wait([
        CategoriesService.categories(),
        CategoriesService.trendCategories(),
      ]);

      if (mounted) {
        setState(() {
          categories = results[0];
          trendCategories = results[1];
          isLoading = false;
          isInitialLoad = false;
        });
      }

      // تحديث البيانات في الخلفية إذا انتهت صلاحية التخزين المؤقت قريباً
      _refreshDataInBackground();
      
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات من التخزين المؤقت: $e');
      await _loadFromServer();
    }
  }

  /// تحميل البيانات من الخادم
  Future<void> _loadFromServer() async {
    try {
      // تحميل متوازي من الخادم
      final results = await Future.wait([
        CategoriesService.categories(),
        CategoriesService.trendCategories(),
      ]);

      if (mounted) {
        setState(() {
          categories = results[0];
          trendCategories = results[1];
          isLoading = false;
          isInitialLoad = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات من الخادم: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
          isInitialLoad = false;
        });
      }
    }
  }

  /// تحديث البيانات في الخلفية
  void _refreshDataInBackground() {
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        CategoriesService.preloadCategoriesData();
      }
    });
  }

  /// إعادة تحميل البيانات (Pull to refresh)
  Future<void> _refreshData() async {
    await CategoriesService.refreshCategories();
    await _loadFromServer();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    
    return Consumer<DrawerProvider>(
      builder: (context, drawerProvider, _) {
        return directionality(
          child: Scaffold(
            appBar: appbar(
              title: appText.categories,
              leftIcon: AppAssets.menuSvg,
              onTapLeftIcon: () {
                drawerController.showDrawer();
              },
              isBasket: true
            ),

            body: RepaintBoundary(
              child: RefreshIndicator(
                color: const Color(0xFF754FFE),
                onRefresh: _refreshData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(
                    parent: BouncingScrollPhysics(),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [

                      space(15),

                      // عنوان الطبقات الشائعة
                      RepaintBoundary(
                        child: Padding(
                          padding: padding(),
                          child: Text(
                            appText.trending,
                            style: style16Regular(),
                          ),
                        ),
                      ),

                      space(14),

                      // الطبقات الشائعة (أفقية)
                      RepaintBoundary(
                        child: _buildTrendingCategories(),
                      ),

                      space(30),

                      // عنوان تصفح الطبقات
                      RepaintBoundary(
                        child: Padding(
                          padding: padding(),
                          child: Text(
                            appText.browseCategories,
                            style: style16Regular().copyWith(color: grey3A),
                          ),
                        ),
                      ),

                      space(14),

                      // قائمة الطبقات (عمودية)
                      RepaintBoundary(
                        child: _buildCategoriesList(),
                      ),

                      space(120),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم الطبقات الشائعة
  Widget _buildTrendingCategories() {
    return SizedBox(
      width: getSize().width,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        padding: padding(),
        child: Row(
          children: List.generate(
            isLoading && isInitialLoad ? 3 : trendCategories.length, 
            (index) {
              if (isLoading && isInitialLoad) {
                return OptimizedShimmerComponent.optimizedHorizontalCategoryShimmer();
              }
              
              return RepaintBoundary(
                key: ValueKey('trend_category_$index'),
                child: horizontalCategoryItem(
                  trendCategories[index].color ?? green77(), 
                  trendCategories[index].icon ?? '', 
                  trendCategories[index].title ?? '', 
                  trendCategories[index].webinarsCount?.toString() ?? '0', 
                  () {
                    nextRoute(
                      FilterCategoryPage.pageName, 
                      arguments: trendCategories[index]
                    );
                  }
                ),
              );
            }
          ),
        ),
      ),
    );
  }

  /// بناء قائمة الطبقات الرئيسية
  Widget _buildCategoriesList() {
    if (isLoading && isInitialLoad) {
      return Container(
        width: getSize().width,
        margin: padding(),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius(),
        ),
        child: OptimizedShimmerComponent.buildOptimizedShimmerList(
          itemBuilder: () => OptimizedShimmerComponent.optimizedCategoryItemShimmer(),
          itemCount: 8,
        ),
      );
    }

    return Container(
      width: getSize().width,
      margin: padding(),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius(),
      ),
      child: Column(
        children: List.generate(
          categories.length, 
          (index) {
            return RepaintBoundary(
              key: ValueKey('category_${categories[index].id}'),
              child: _buildCategoryItem(categories[index], index),
            );
          }
        ),
      ),
    );
  }

  /// بناء عنصر طبقة واحدة
  Widget _buildCategoryItem(CategoryModel category, int index) {
    return Column(
      children: [
        Container(
          width: getSize().width,
          padding: padding(),
          child: GestureDetector(
            onTap: () {
              // استخدام debounce لتجنب الضغط المتكرر
              PerformanceUtils.debounce(() {
                if (category.subCategories?.isNotEmpty ?? false) {
                  setState(() {
                    category.isOpen = !category.isOpen;
                  });
                } else {
                  nextRoute(FilterCategoryPage.pageName, arguments: category);
                }
              });
            },
            behavior: HitTestBehavior.opaque,
            child: Row(
              children: [
                // أيقونة الطبقة
                ClipRRect(
                  borderRadius: BorderRadius.circular(17),
                  child: fadeInImage(category.icon ?? '', 34, 34),
                ),

                space(0, width: 10),

                // معلومات الطبقة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        category.title ?? '',
                        style: style14Bold(),
                      ),
                      
                      Text(
                        '${category.webinarsCount} ${appText.courses}',
                        style: style12Regular().copyWith(color: greyA5),
                      ),
                    ],
                  ),
                ),

                // سهم التوسيع (إذا كانت هناك طبقات فرعية)
                if (category.subCategories?.isNotEmpty ?? false) 
                  AnimatedRotation(
                    turns: category.isOpen
                        ? 90 / 360
                        : locator<AppLanguage>().isRtl()
                            ? 180 / 360 
                            : 0, 
                    duration: const Duration(milliseconds: 200),
                    child: SvgPicture.asset(AppAssets.arrowRightSvg),
                  ),
              ],
            ),
          ),
        ),

        // الطبقات الفرعية (قابلة للتوسيع)
        if (category.subCategories?.isNotEmpty ?? false)
          AnimatedCrossFade(
            firstChild: _buildSubCategories(category),
            secondChild: SizedBox(width: getSize().width),
            crossFadeState: category.isOpen 
                ? CrossFadeState.showFirst 
                : CrossFadeState.showSecond,
            duration: const Duration(milliseconds: 300),
          ),

        // فاصل
        if (index < categories.length - 1) ...[
          space(15),
          Container(
            width: getSize().width,
            height: 1,
            decoration: BoxDecoration(color: greyF8),
          ),
          space(15),
        ],
      ],
    );
  }

  /// بناء الطبقات الفرعية
  Widget _buildSubCategories(CategoryModel category) {
    return Container(
      width: getSize().width,
      padding: const EdgeInsetsDirectional.only(start: 54, end: 20),
      child: Column(
        children: List.generate(
          category.subCategories?.length ?? 0,
          (index) {
            final subCategory = category.subCategories![index];
            return RepaintBoundary(
              key: ValueKey('sub_category_${subCategory.id}'),
              child: Container(
                width: getSize().width,
                margin: const EdgeInsets.only(bottom: 15),
                child: GestureDetector(
                  onTap: () {
                    PerformanceUtils.debounce(() {
                      nextRoute(FilterCategoryPage.pageName, arguments: subCategory);
                    });
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(13),
                        child: fadeInImage(subCategory.icon ?? '', 26, 26),
                      ),

                      space(0, width: 10),

                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              subCategory.title ?? '',
                              style: style12Regular().copyWith(color: grey3A),
                            ),
                            
                            Text(
                              '${subCategory.webinarsCount} ${appText.courses}',
                              style: style10Regular().copyWith(color: greyA5),
                            ),
                          ],
                        ),
                      ),

                      SvgPicture.asset(
                        AppAssets.arrowRightSvg,
                        colorFilter: ColorFilter.mode(
                          greyA5, 
                          BlendMode.srcIn
                        ),
                        width: 12,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class DashedLineVerticalPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double dashHeight = 6, dashSpace = 5, startY = 0;
    final paint = Paint()
      ..color = Colors.grey.withOpacity(.5)
      ..strokeWidth = .4;
    while (startY < size.height) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashHeight), paint);
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

