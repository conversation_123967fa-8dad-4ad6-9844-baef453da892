import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Performance utilities to help optimize Flutter app performance
class PerformanceUtils {
  PerformanceUtils._();

  /// Debounce function to limit the rate of function calls
  static Timer? _debounceTimer;
  
  static void debounce(VoidCallback action, {Duration delay = const Duration(milliseconds: 300)}) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, action);
  }

  /// Throttle function to limit the rate of function calls
  static DateTime? _lastThrottleTime;
  
  static void throttle(VoidCallback action, {Duration delay = const Duration(milliseconds: 100)}) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || now.difference(_lastThrottleTime!) >= delay) {
      _lastThrottleTime = now;
      action();
    }
  }

  /// Preload and cache images to improve performance
  static Future<void> preloadImages(BuildContext context, List<String> imagePaths) async {
    final List<Future<void>> futures = [];
    
    for (final imagePath in imagePaths) {
      if (imagePath.startsWith('http')) {
        futures.add(precacheImage(NetworkImage(imagePath), context));
      } else {
        futures.add(precacheImage(AssetImage(imagePath), context));
      }
    }
    
    await Future.wait(futures);
  }

  /// Optimize list view performance with automatic keep alive
  static Widget buildOptimizedListItem({
    required Widget child,
    required int index,
    bool keepAlive = false,
  }) {
    if (keepAlive) {
      return _KeepAliveWrapper(child: child);
    }
    return RepaintBoundary(
      key: ValueKey(index),
      child: child,
    );
  }

  /// Create an optimized ListView.builder with performance improvements
  static Widget buildOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    double? cacheExtent,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    Axis scrollDirection = Axis.vertical,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      cacheExtent: cacheExtent ?? 250.0, // Preload items offscreen
      itemCount: itemCount,
      shrinkWrap: shrinkWrap,
      physics: physics ?? const BouncingScrollPhysics(),
      scrollDirection: scrollDirection,
      addAutomaticKeepAlives: false, // Disable automatic keep alive
      addRepaintBoundaries: true, // Add repaint boundaries
      addSemanticIndexes: true, // Add semantic indexes for accessibility
      itemBuilder: (context, index) {
        return RepaintBoundary(
          key: ValueKey(index),
          child: itemBuilder(context, index),
        );
      },
    );
  }

  /// Create an optimized GridView.builder with performance improvements
  static Widget buildOptimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    EdgeInsets? padding,
    double? cacheExtent,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    Axis scrollDirection = Axis.vertical,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      cacheExtent: cacheExtent ?? 250.0,
      itemCount: itemCount,
      gridDelegate: gridDelegate,
      shrinkWrap: shrinkWrap,
      physics: physics ?? const BouncingScrollPhysics(),
      scrollDirection: scrollDirection,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: true,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          key: ValueKey(index),
          child: itemBuilder(context, index),
        );
      },
    );
  }

  /// Optimize image loading with caching and compression
  static Widget buildOptimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit? fit,
    Widget? placeholder,
    Widget? errorWidget,
    bool enableMemoryCache = true,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    if (imagePath.startsWith('http')) {
      // For network images, use cached_network_image
      return Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
        loadingBuilder: placeholder != null
            ? (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return placeholder;
              }
            : null,
        errorBuilder: errorWidget != null
            ? (context, error, stackTrace) => errorWidget
            : null,
      );
    } else {
      // For asset images
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
        errorBuilder: errorWidget != null
            ? (context, error, stackTrace) => errorWidget
            : null,
      );
    }
  }

  /// Reduce over-drawing by using const constructors where possible
  static Widget buildConstSizedBox({
    double? width,
    double? height,
    Widget? child,
  }) {
    return SizedBox(
      width: width,
      height: height,
      child: child,
    );
  }

  /// Memory-efficient way to build separators
  static Widget buildSeparator({
    double height = 1.0,
    Color color = Colors.grey,
    EdgeInsets margin = EdgeInsets.zero,
  }) {
    return Container(
      height: height,
      margin: margin,
      color: color,
    );
  }

  /// Optimize text widgets for better performance
  static Widget buildOptimizedText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      softWrap: softWrap,
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
        applyHeightToLastDescent: false,
      ),
    );
  }

  /// Efficient way to handle multiple animations
  static AnimationController createOptimizedAnimationController({
    required TickerProvider vsync,
    Duration duration = const Duration(milliseconds: 300),
    double? value,
    double lowerBound = 0.0,
    double upperBound = 1.0,
  }) {
    return AnimationController(
      duration: duration,
      value: value,
      lowerBound: lowerBound,
      upperBound: upperBound,
      vsync: vsync,
    );
  }

  /// Batch multiple setState calls into a single frame
  static void batchSetState(VoidCallback fn) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      fn();
    });
  }

  /// Efficiently handle scroll notifications
  static bool optimizedScrollNotification(
    ScrollNotification notification,
    VoidCallback onScrollStart,
    VoidCallback onScrollUpdate,
    VoidCallback onScrollEnd,
  ) {
    if (notification is ScrollStartNotification) {
      onScrollStart();
    } else if (notification is ScrollUpdateNotification) {
      throttle(onScrollUpdate);
    } else if (notification is ScrollEndNotification) {
      onScrollEnd();
    }
    return true;
  }

  /// Reduce memory usage by disposing unused resources
  static void disposeResources(List<dynamic> resources) {
    for (final resource in resources) {
      try {
        if (resource is AnimationController) {
          resource.dispose();
        } else if (resource is ScrollController) {
          resource.dispose();
        } else if (resource is PageController) {
          resource.dispose();
        } else if (resource is TabController) {
          resource.dispose();
        } else if (resource is Timer) {
          resource.cancel();
        } else if (resource is StreamSubscription) {
          resource.cancel();
        }
      } catch (e) {
        debugPrint('Error disposing resource: $e');
      }
    }
  }

  /// Optimize heavy computations by using isolates
  static Future<T> runHeavyComputation<T>(T Function() computation) async {
    return await Future.microtask(computation);
  }

  /// Preload critical data for faster access
  static Future<void> preloadCriticalData(List<Future<void>> futures) async {
    await Future.wait(
      futures,
      eagerError: false,
    );
  }

  /// Optimize shader compilation
  static Future<void> warmUpShaders() async {
    const Size size = Size(100, 100);
    // Simplified shader warm-up without using ui.Picture
    // This helps with initial rendering performance
    final paint = Paint();
    paint.color = Colors.blue;
    
    // Basic warmup operations
    await Future.delayed(const Duration(milliseconds: 1));
  }
}

/// Wrapper widget to keep alive list items
class _KeepAliveWrapper extends StatefulWidget {
  final Widget child;

  const _KeepAliveWrapper({required this.child});

  @override
  State<_KeepAliveWrapper> createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<_KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}

/// Extension to add performance methods to ScrollController
extension ScrollControllerPerformance on ScrollController {
  /// Animate to position with performance optimizations
  Future<void> animateToOptimized(
    double offset, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) async {
    if (!hasClients) return;
    
    await animateTo(
      offset.clamp(position.minScrollExtent, position.maxScrollExtent),
      duration: duration,
      curve: curve,
    );
  }

  /// Check if scroll position is near the end
  bool get isNearEnd {
    if (!hasClients) return false;
    return position.pixels >= position.maxScrollExtent - 200;
  }

  /// Check if scroll position is at the top
  bool get isAtTop {
    if (!hasClients) return false;
    return position.pixels <= position.minScrollExtent + 10;
  }
} 