import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:webinar/config/notification.dart';
import 'package:webinar/common/common.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    await setupFlutterNotifications();
    await _setupFirebaseListeners();
  }

  /// إعداد مستمعي Firebase
  Future<void> _setupFirebaseListeners() async {
    // معالجة الإشعارات في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // معالجة النقر على الإشعار
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
    
    // التحقق من الإشعار الأولي
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }
  }

  /// معالجة الإشعارات في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    print('إشعار في المقدمة: ${message.notification?.title}');
    showFlutterNotification(message);
  }

  /// معالجة النقر على الإشعار
  void _handleMessageOpenedApp(RemoteMessage message) {
    print('تم النقر على الإشعار: ${message.data}');
    _navigateToScreen(message.data);
  }

  /// التنقل إلى الشاشة المناسبة حسب نوع الإشعار
  void _navigateToScreen(Map<String, dynamic> data) {
    if (!data.containsKey('type')) return;

    String type = data['type'];
    BuildContext? context = _getCurrentContext();
    
    if (context == null) return;

    switch (type) {
      case 'course':
        _navigateToCourse(context, data);
        break;
      case 'message':
        _navigateToMessages(context, data);
        break;
      case 'assignment':
        _navigateToAssignment(context, data);
        break;
      case 'quiz':
        _navigateToQuiz(context, data);
        break;
      case 'meeting':
        _navigateToMeeting(context, data);
        break;
      case 'certificate':
        _navigateToCertificate(context, data);
        break;
      default:
        _navigateToNotifications(context);
    }
  }

  /// التنقل إلى صفحة الكورس
  void _navigateToCourse(BuildContext context, Map<String, dynamic> data) {
    String? courseId = data['course_id'];
    if (courseId != null) {
      Navigator.pushNamed(
        context,
        '/single-course',
        arguments: {'id': courseId},
      );
    }
  }

  /// التنقل إلى صفحة الرسائل
  void _navigateToMessages(BuildContext context, Map<String, dynamic> data) {
    Navigator.pushNamed(context, '/support-message');
  }

  /// التنقل إلى صفحة المهام
  void _navigateToAssignment(BuildContext context, Map<String, dynamic> data) {
    String? assignmentId = data['assignment_id'];
    if (assignmentId != null) {
      Navigator.pushNamed(
        context,
        '/assignment-overview',
        arguments: {'id': assignmentId},
      );
    } else {
      Navigator.pushNamed(context, '/assignments');
    }
  }

  /// التنقل إلى صفحة الاختبار
  void _navigateToQuiz(BuildContext context, Map<String, dynamic> data) {
    String? quizId = data['quiz_id'];
    if (quizId != null) {
      Navigator.pushNamed(
        context,
        '/quiz-info',
        arguments: {'id': quizId},
      );
    } else {
      Navigator.pushNamed(context, '/quizzes');
    }
  }

  /// التنقل إلى صفحة الاجتماع
  void _navigateToMeeting(BuildContext context, Map<String, dynamic> data) {
    String? meetingId = data['meeting_id'];
    if (meetingId != null) {
      Navigator.pushNamed(
        context,
        '/meeting-details',
        arguments: {'id': meetingId},
      );
    } else {
      Navigator.pushNamed(context, '/meetings');
    }
  }

  /// التنقل إلى صفحة الشهادة
  void _navigateToCertificate(BuildContext context, Map<String, dynamic> data) {
    Navigator.pushNamed(context, '/certificates');
  }

  /// التنقل إلى صفحة الإشعارات
  void _navigateToNotifications(BuildContext context) {
    Navigator.pushNamed(context, '/notifications');
  }

  /// الحصول على السياق الحالي
  BuildContext? _getCurrentContext() {
    return navigatorKey.currentContext;
  }

  /// إرسال إشعار محلي
  Future<void> sendLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    await showCustomNotification(
      title: title,
      body: body,
      payload: data != null ? jsonEncode(data) : null,
    );
  }

  /// جدولة إشعار
  Future<void> scheduleLocalNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    Map<String, dynamic>? data,
  }) async {
    await scheduleNotification(
      title: title,
      body: body,
      scheduledDate: scheduledDate,
      payload: data != null ? jsonEncode(data) : null,
    );
  }

  /// الحصول على FCM Token
  Future<String?> getToken() async {
    return await getFCMToken();
  }

  /// الاشتراك في موضوع
  Future<void> subscribe(String topic) async {
    await subscribeToTopic(topic);
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribe(String topic) async {
    await unsubscribeFromTopic(topic);
  }

  /// إلغاء جميع الإشعارات
  Future<void> clearAllNotifications() async {
    await cancelAllNotifications();
  }
} 