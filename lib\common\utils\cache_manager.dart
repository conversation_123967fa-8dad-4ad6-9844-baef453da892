import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مدير التخزين المؤقت لتحسين أداء التطبيق
class CacheManager {
  CacheManager._();
  static final CacheManager _instance = CacheManager._();
  static CacheManager get instance => _instance;

  SharedPreferences? _prefs;
  Directory? _cacheDir;
  
  // مدة انتهاء صلاحية التخزين المؤقت (بالدقائق)
  static const int _cacheExpiryMinutes = 30;
  static const int _shortCacheExpiryMinutes = 5;
  static const int _longCacheExpiryMinutes = 120;

  /// تهيئة مدير التخزين المؤقت
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _cacheDir = await getApplicationCacheDirectory();
  }

  /// تخزين البيانات مع تاريخ انتهاء الصلاحية
  Future<void> storeData(String key, dynamic data, {int? expiryMinutes}) async {
    await _ensureInitialized();
    
    final expiryTime = DateTime.now().add(
      Duration(minutes: expiryMinutes ?? _cacheExpiryMinutes)
    );
    
    final cacheData = {
      'data': data,
      'expiry': expiryTime.millisecondsSinceEpoch,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    await _prefs!.setString(key, jsonEncode(cacheData));
  }

  /// استرجاع البيانات من التخزين المؤقت
  Future<T?> getData<T>(String key) async {
    await _ensureInitialized();
    
    final cachedString = _prefs!.getString(key);
    if (cachedString == null) return null;
    
    try {
      final cacheData = jsonDecode(cachedString);
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(cacheData['expiry']);
      
      // فحص انتهاء الصلاحية
      if (DateTime.now().isAfter(expiryTime)) {
        await _prefs!.remove(key);
        return null;
      }
      
      return cacheData['data'] as T?;
    } catch (e) {
      await _prefs!.remove(key);
      return null;
    }
  }

  /// تخزين قائمة البيانات
  Future<void> storeList(String key, List<dynamic> list, {int? expiryMinutes}) async {
    await storeData(key, list, expiryMinutes: expiryMinutes);
  }

  /// استرجاع قائمة البيانات
  Future<List<T>?> getList<T>(String key) async {
    final data = await getData<List<dynamic>>(key);
    if (data == null) return null;
    
    try {
      return data.cast<T>();
    } catch (e) {
      return null;
    }
  }

  /// تخزين الصورة في الملفات
  Future<void> storeImage(String key, List<int> imageBytes) async {
    await _ensureInitialized();
    
    final file = File('${_cacheDir!.path}/images/$key');
    await file.create(recursive: true);
    await file.writeAsBytes(imageBytes);
    
    // حفظ معلومات الصورة مع تاريخ الانتهاء
    await storeData('image_info_$key', {
      'path': file.path,
      'size': imageBytes.length,
    }, expiryMinutes: _longCacheExpiryMinutes);
  }

  /// استرجاع الصورة من الملفات
  Future<File?> getImage(String key) async {
    await _ensureInitialized();
    
    final imageInfo = await getData<Map<String, dynamic>>('image_info_$key');
    if (imageInfo == null) return null;
    
    final file = File(imageInfo['path']);
    if (await file.exists()) {
      return file;
    } else {
      // إزالة المعلومات إذا لم يعد الملف موجود
      await _prefs!.remove('image_info_$key');
      return null;
    }
  }

  /// فحص وجود البيانات في التخزين المؤقت
  Future<bool> hasValidData(String key) async {
    final data = await getData(key);
    return data != null;
  }

  /// تخزين البيانات مع أولوية عالية (تخزين طويل المدى)
  Future<void> storeHighPriorityData(String key, dynamic data) async {
    await storeData(key, data, expiryMinutes: _longCacheExpiryMinutes);
  }

  /// تخزين البيانات المؤقتة (تخزين قصير المدى)
  Future<void> storeTemporaryData(String key, dynamic data) async {
    await storeData(key, data, expiryMinutes: _shortCacheExpiryMinutes);
  }

  /// حذف بيانات معينة
  Future<void> removeData(String key) async {
    await _ensureInitialized();
    await _prefs!.remove(key);
  }

  /// حذف جميع البيانات المؤقتة
  Future<void> clearAllCache() async {
    await _ensureInitialized();
    
    // حذف البيانات من SharedPreferences
    final keys = _prefs!.getKeys().where((key) => 
      key.startsWith('cache_') || 
      key.startsWith('image_info_')
    ).toList();
    
    for (final key in keys) {
      await _prefs!.remove(key);
    }
    
    // حذف ملفات الصور
    final imageDir = Directory('${_cacheDir!.path}/images');
    if (await imageDir.exists()) {
      await imageDir.delete(recursive: true);
    }
  }

  /// حذف البيانات المنتهية الصلاحية
  Future<void> cleanExpiredCache() async {
    await _ensureInitialized();
    
    final keys = _prefs!.getKeys().toList();
    final expiredKeys = <String>[];
    
    for (final key in keys) {
      if (key.startsWith('cache_') || key.startsWith('image_info_')) {
        final data = await getData(key);
        if (data == null) {
          expiredKeys.add(key);
        }
      }
    }
    
    for (final key in expiredKeys) {
      await _prefs!.remove(key);
    }
  }

  /// الحصول على حجم التخزين المؤقت
  Future<Map<String, dynamic>> getCacheStats() async {
    await _ensureInitialized();
    
    final keys = _prefs!.getKeys().where((key) => 
      key.startsWith('cache_') || 
      key.startsWith('image_info_')
    ).toList();
    
    int totalEntries = keys.length;
    int totalSize = 0;
    int expiredEntries = 0;
    
    for (final key in keys) {
      final cachedString = _prefs!.getString(key);
      if (cachedString != null) {
        totalSize += cachedString.length;
        
        try {
          final cacheData = jsonDecode(cachedString);
          final expiryTime = DateTime.fromMillisecondsSinceEpoch(cacheData['expiry']);
          if (DateTime.now().isAfter(expiryTime)) {
            expiredEntries++;
          }
        } catch (e) {
          expiredEntries++;
        }
      }
    }
    
    return {
      'totalEntries': totalEntries,
      'totalSizeBytes': totalSize,
      'expiredEntries': expiredEntries,
      'cacheDirectory': _cacheDir?.path,
    };
  }

  /// التأكد من تهيئة المدير
  Future<void> _ensureInitialized() async {
    if (_prefs == null || _cacheDir == null) {
      await initialize();
    }
  }
}

/// مفاتيح التخزين المؤقت الشائعة
class CacheKeys {
  static const String categories = 'cache_categories';
  static const String trendCategories = 'cache_trend_categories';
  static const String featuredCourses = 'cache_featured_courses';
  static const String newCourses = 'cache_new_courses';
  static const String bestRatedCourses = 'cache_best_rated_courses';
  static const String bestSellingCourses = 'cache_best_selling_courses';
  static const String discountCourses = 'cache_discount_courses';
  static const String freeCourses = 'cache_free_courses';
  static const String blogPosts = 'cache_blog_posts';
  static const String blogCategories = 'cache_blog_categories';
  static const String userProfile = 'cache_user_profile';
  static const String notifications = 'cache_notifications';
  static const String cart = 'cache_cart';
  static const String rewards = 'cache_rewards';
  
  // مفاتيح الفلاتر
  static String categoryFilter(int categoryId) => 'cache_category_filter_$categoryId';
  static String coursesForCategory(int categoryId) => 'cache_courses_category_$categoryId';
  static String courseContent(int courseId) => 'cache_course_content_$courseId';
} 