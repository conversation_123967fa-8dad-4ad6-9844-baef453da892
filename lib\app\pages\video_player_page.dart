import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerPage extends StatefulWidget {
  const VideoPlayerPage({Key? key}) : super(key: key);

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late VideoPlayerController _controller;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  bool _isBuffering = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    
    // تهيئة animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut)
    );
    
    _initializeVideo();
    _startControlsTimer();
  }

  void _initializeVideo() async {
    try {
      // تحميل الفيديو من الملفات المحلية مع تحسين الأداء
      _controller = VideoPlayerController.asset(
        'assets/video/IMG_4419.MP4',
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: false,
          allowBackgroundPlayback: false,
        ),
      );
      
      // تهيئة متقدمة للفيديو
      await _controller.initialize();
      
      // تحسين جودة التشغيل
      await _controller.setLooping(false);
      await _controller.setVolume(1.0);
      
      if (mounted) {
        setState(() {
          _isInitialized = true;
          _totalDuration = _controller.value.duration;
        });
        
        _fadeController.forward();
        
        // مراقبة تقدم الفيديو مع تحسين الأداء
        _controller.addListener(_videoListener);
      }
      
    } catch (e) {
      print('خطأ في تحميل الفيديو: $e');
      if (mounted) {
        _showErrorDialog();
      }
    }
  }

  void _videoListener() {
    if (!mounted || !_controller.value.isInitialized) return;
    
    final newPosition = _controller.value.position;
    final isPlaying = _controller.value.isPlaying;
    final isBuffering = _controller.value.isBuffering;
    
    // تحديث الحالة فقط عند الحاجة لتقليل إعادة البناء
    if (_currentPosition != newPosition || 
        _isPlaying != isPlaying || 
        _isBuffering != isBuffering) {
      setState(() {
        _currentPosition = newPosition;
        _isPlaying = isPlaying;
        _isBuffering = isBuffering;
      });
    }
  }

  void _togglePlayPause() {
    if (!_controller.value.isInitialized) return;
    
    setState(() {
      if (_controller.value.isPlaying) {
        _controller.pause();
      } else {
        _controller.play();
      }
    });
    _showControlsTemporarily();
  }

  void _seekTo(Duration position) {
    if (!_controller.value.isInitialized) return;
    
    _controller.seekTo(position);
    _showControlsTemporarily();
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    _startControlsTimer();
  }

  void _startControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _showErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: const Text('لا يمكن تشغيل الفيديو. تأكد من وجود الملف في المسار الصحيح.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  void dispose() {
    _controller.removeListener(_videoListener);
    _controller.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: RepaintBoundary(
          child: Stack(
            children: [
              // الفيديو
              Center(
                child: _isInitialized
                    ? RepaintBoundary(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: AspectRatio(
                            aspectRatio: _controller.value.aspectRatio,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _showControls = !_showControls;
                                });
                                if (_showControls) {
                                  _startControlsTimer();
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: VideoPlayer(_controller),
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    : const _LoadingWidget(),
              ),
              
              // أدوات التحكم
              if (_showControls && _isInitialized)
                RepaintBoundary(
                  child: _VideoControls(
                    controller: _controller,
                    isPlaying: _isPlaying,
                    isBuffering: _isBuffering,
                    currentPosition: _currentPosition,
                    totalDuration: _totalDuration,
                    onPlayPause: _togglePlayPause,
                    onSeek: _seekTo,
                    formatDuration: _formatDuration,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Color(0xFF754FFE),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل الفيديو...',
            style: TextStyle(color: Colors.grey, fontSize: 16),
          ),
        ],
      ),
    );
  }
}

class _VideoControls extends StatelessWidget {
  final VideoPlayerController controller;
  final bool isPlaying;
  final bool isBuffering;
  final Duration currentPosition;
  final Duration totalDuration;
  final VoidCallback onPlayPause;
  final Function(Duration) onSeek;
  final String Function(Duration) formatDuration;

  const _VideoControls({
    required this.controller,
    required this.isPlaying,
    required this.isBuffering,
    required this.currentPosition,
    required this.totalDuration,
    required this.onPlayPause,
    required this.onSeek,
    required this.formatDuration,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
              Colors.transparent,
              Colors.black.withOpacity(0.7),
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Column(
          children: [
            // شريط العنوان العلوي
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  const Expanded(
                    child: Text(
                      'مشغل الفيديو',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48), // للتوازن
                ],
              ),
            ),
            
            const Spacer(),
            
            // مؤشر التحميل
            if (isBuffering)
              const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFF754FFE),
                ),
              ),
            
            const Spacer(),
            
            // أدوات التحكم السفلية
            Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // شريط التقدم
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: const Color(0xFF754FFE),
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: const Color(0xFF754FFE),
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                      overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
                    ),
                    child: Slider(
                      value: currentPosition.inMilliseconds.toDouble(),
                      max: totalDuration.inMilliseconds.toDouble(),
                      onChanged: (value) {
                        onSeek(Duration(milliseconds: value.toInt()));
                      },
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // أدوات التحكم الرئيسية
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // الوقت الحالي
                      Text(
                        formatDuration(currentPosition),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                      
                      // أزرار التحكم
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: Icon(
                              Icons.replay_10,
                              color: Colors.white,
                              size: 28,
                            ),
                            onPressed: () {
                              final newPosition = currentPosition - const Duration(seconds: 10);
                              onSeek(newPosition < Duration.zero ? Duration.zero : newPosition);
                            },
                          ),
                          
                          const SizedBox(width: 16),
                          
                          GestureDetector(
                            onTap: onPlayPause,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: const Color(0xFF754FFE),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Icon(
                                isPlaying ? Icons.pause : Icons.play_arrow,
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                          ),
                          
                          const SizedBox(width: 16),
                          
                          IconButton(
                            icon: const Icon(
                              Icons.forward_10,
                              color: Colors.white,
                              size: 28,
                            ),
                            onPressed: () {
                              final newPosition = currentPosition + const Duration(seconds: 10);
                              onSeek(newPosition > totalDuration ? totalDuration : newPosition);
                            },
                          ),
                        ],
                      ),
                      
                      // الوقت الإجمالي
                      Text(
                        formatDuration(totalDuration),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 