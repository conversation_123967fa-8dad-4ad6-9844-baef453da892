/// ملف تكوين الإشعارات
class NotificationConfig {
  // معرفات قنوات الإشعارات
  static const String highImportanceChannelId = 'high_importance_channel';
  static const String customChannelId = 'custom_channel';
  static const String scheduledChannelId = 'scheduled_channel';
  
  // أسماء قنوات الإشعارات
  static const String highImportanceChannelName = 'High Importance Notifications';
  static const String customChannelName = 'Custom Notifications';
  static const String scheduledChannelName = 'Scheduled Notifications';
  
  // أوصاف قنوات الإشعارات
  static const String highImportanceChannelDesc = 'This channel is used for important notifications.';
  static const String customChannelDesc = 'Custom local notifications';
  static const String scheduledChannelDesc = 'Scheduled notifications';
  
  // أيقونات الإشعارات
  static const String notificationIcon = '@drawable/ic_notification';
  static const String largeNotificationIcon = '@mipmap/ic_notification';
  
  // أنواع الإشعارات
  static const String courseType = 'course';
  static const String assignmentType = 'assignment';
  static const String quizType = 'quiz';
  static const String meetingType = 'meeting';
  static const String messageType = 'message';
  static const String certificateType = 'certificate';
  static const String generalType = 'general';
  
  // مواضيع Firebase
  static const String generalTopic = 'general';
  static const String coursesTopic = 'courses';
  static const String assignmentsTopic = 'assignments';
  static const String quizzesTopic = 'quizzes';
  static const String meetingsTopic = 'meetings';
  
  // مسارات التنقل
  static const Map<String, String> navigationRoutes = {
    courseType: '/single-course',
    assignmentType: '/assignments',
    quizType: '/quizzes',
    meetingType: '/meetings',
    messageType: '/support-message',
    certificateType: '/certificates',
    generalType: '/notifications',
  };
  
  // رسائل الإشعارات الافتراضية
  static const Map<String, Map<String, String>> defaultMessages = {
    courseType: {
      'title': 'كورس جديد متاح!',
      'body': 'تم إضافة كورس جديد يمكنك الاطلاع عليه',
    },
    assignmentType: {
      'title': 'مهمة جديدة!',
      'body': 'لديك مهمة جديدة تحتاج إلى إنجازها',
    },
    quizType: {
      'title': 'اختبار جديد!',
      'body': 'يمكنك الآن إجراء اختبار جديد',
    },
    meetingType: {
      'title': 'اجتماع قادم!',
      'body': 'لديك اجتماع مجدول قريباً',
    },
    messageType: {
      'title': 'رسالة جديدة!',
      'body': 'لديك رسالة جديدة في صندوق الوارد',
    },
    certificateType: {
      'title': 'شهادة جديدة!',
      'body': 'تم إصدار شهادة جديدة لك',
    },
  };
  
  // إعدادات الإشعارات
  static const bool enableVibration = true;
  static const bool enableSound = true;
  static const bool enableBadge = true;
  static const bool enableLights = true;
  
  // ألوان الإشعارات
  static const int notificationColor = 0xFF754FFE; // اللون البنفسجي للتطبيق
  
  /// الحصول على رسالة افتراضية حسب النوع
  static Map<String, String>? getDefaultMessage(String type) {
    return defaultMessages[type];
  }
  
  /// الحصول على مسار التنقل حسب النوع
  static String? getNavigationRoute(String type) {
    return navigationRoutes[type];
  }
  
  /// التحقق من صحة نوع الإشعار
  static bool isValidNotificationType(String type) {
    return navigationRoutes.containsKey(type);
  }
  
  /// الحصول على جميع أنواع الإشعارات المدعومة
  static List<String> getSupportedTypes() {
    return navigationRoutes.keys.toList();
  }
  
  /// الحصول على جميع المواضيع المتاحة
  static List<String> getAvailableTopics() {
    return [
      generalTopic,
      coursesTopic,
      assignmentsTopic,
      quizzesTopic,
      meetingsTopic,
    ];
  }
} 