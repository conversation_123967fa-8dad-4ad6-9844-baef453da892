<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Webinar</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>webinar</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<array>
					<string>payment-success</string>
					<string>payment-failed</string>
				</array>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>academyapp</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb715781717022310</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLIconFile</key>
				<string>GoogleService-Info</string>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.346142227155-30a9drvvvduc0qboegip572sh6e30aeg</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FDMaximumConcurrentTasks</key>
		<integer>5</integer>
		<key>FLTEnableImpeller</key>
		<false/>
		<key>FacebookAppID</key>
		<string>715781717022310</string>
		<key>FacebookAutoLogAppEventsEnabled</key>
		<false/>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>Webinar</string>
		<key>GIDClientID</key>
		<string>346142227155-30a9drvvvduc0qboegip572sh6e30aeg.apps.googleusercontent.com</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fb-messenger-share-api</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>NSBonjourServices</key>
		<array>
			<string>_googlecast._tcp</string>
		</array>
		<key>NSCalendarsUsageDescription</key>
		<string>We Need this Access to Set Webinar Time</string>
		<key>NSCameraUsageDescription</key>
		<string>This app requires Camera permission.</string>
		<key>NSContactsUsageDescription</key>
		<string>We Need this Access to Set Webinar Time</string>
		<key>NSLocalNetworkUsageDescription</key>
		<string>Used to search for chromecast devices</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>This app requires Mic permission.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Choose a photo to create an ad</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>This example recognizes words as you speak them and displays them.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>io.flutter.embedded_views_preview</key>
		<string>YES</string>
	</dict>
</plist>
