import 'package:flutter/material.dart';
import 'package:webinar/app/models/category_model.dart';
import 'package:webinar/app/models/course_model.dart';
import 'package:webinar/app/models/filter_model.dart';
import 'package:webinar/app/pages/main_page/categories_page/filter_category_page/dynamiclly_filter.dart';
import 'package:webinar/app/pages/main_page/categories_page/filter_category_page/options_filter.dart';
import 'package:webinar/app/providers/filter_course_provider.dart';
import 'package:webinar/app/services/guest_service/categories_service.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/shimmer_component.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/common/utils/tablet_detector.dart';
import 'package:webinar/common/utils/performance_utils.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/locator.dart';

import '../../../../services/guest_service/course_service.dart';
import '../../../../../common/components.dart';
import '../../../../widgets/main_widget/home_widget/home_widget.dart';

class OptimizedFilterCategoryPage extends StatefulWidget {
  static const String pageName = '/optimized-filter-category';
  const OptimizedFilterCategoryPage({super.key});

  @override
  State<OptimizedFilterCategoryPage> createState() => _OptimizedFilterCategoryPageState();
}

class _OptimizedFilterCategoryPageState extends State<OptimizedFilterCategoryPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {

  bool isLoading = false;
  bool isGrid = true;
  bool _isLoadingFeatured = false;
  bool _isInitialized = false;

  CategoryModel? category;

  List<CourseModel> data = [];
  List<CourseModel> featuredListData = [];
  List<FilterModel> filters = [];
  
  late ScrollController scrollController;
  late PageController sliderPageController;
  int currentSliderIndex = 0;

  // Performance optimization: Keep state alive
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with performance optimizations
    scrollController = ScrollController();
    sliderPageController = PageController(viewportFraction: 0.9);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      category = (ModalRoute.of(context)!.settings.arguments as CategoryModel?);
      _initializeData();
    });

    _setupScrollListener();
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      // Use throttle to reduce listener calls
      PerformanceUtils.throttle(() {
        if (scrollController.isNearEnd && !isLoading) {
          _loadMoreData();
        }
      });
    });
  }

  void _initializeData() async {
    if (_isInitialized || category == null) return;
    
    setState(() {
      _isInitialized = true;
    });

    // Preload critical data in parallel
    await PerformanceUtils.preloadCriticalData([
      _getInitialData(),
      _getFilters(),
      _getFeatured(),
    ]);
  }

  Future<void> _getInitialData() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
    });

    try {
      final newData = await CourseService.getAll(
        offset: 0,
        cat: category?.id?.toString(),
        filterOption: locator<FilterCourseProvider>().filterSelected,
        upcoming: locator<FilterCourseProvider>().upcoming,
        free: locator<FilterCourseProvider>().free,
        discount: locator<FilterCourseProvider>().discount,
        downloadable: locator<FilterCourseProvider>().downloadable,
        sort: locator<FilterCourseProvider>().sort,
        bundle: locator<FilterCourseProvider>().bundleCourse,
        reward: locator<FilterCourseProvider>().rewardCourse
      );

      if (mounted) {
        setState(() {
          data = newData;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
    });

    try {
      final newData = await CourseService.getAll(
        offset: data.length,
        cat: category?.id?.toString(),
        filterOption: locator<FilterCourseProvider>().filterSelected,
        upcoming: locator<FilterCourseProvider>().upcoming,
        free: locator<FilterCourseProvider>().free,
        discount: locator<FilterCourseProvider>().discount,
        downloadable: locator<FilterCourseProvider>().downloadable,
        sort: locator<FilterCourseProvider>().sort,
        bundle: locator<FilterCourseProvider>().bundleCourse,
        reward: locator<FilterCourseProvider>().rewardCourse
      );

      if (mounted) {
        setState(() {
          data.addAll(newData);
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _getFilters() async {
    if (category == null) return;

    try {
      final newFilters = await CategoriesService.getFilters(category!.id!);
      
      if (mounted) {
        locator<FilterCourseProvider>().filters = newFilters;
        setState(() {
          filters = newFilters;
        });
      }
    } catch (e) {
      debugPrint('Error loading filters: $e');
    }
  }

  Future<void> _getFeatured() async {
    if (category == null || _isLoadingFeatured) return;

    setState(() {
      _isLoadingFeatured = true;
    });

    try {
      final featured = await CourseService.featuredCourse(cat: category!.id!.toString());
      
      if (mounted) {
        setState(() {
          featuredListData = featured;
          _isLoadingFeatured = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingFeatured = false;
        });
      }
    }
  }

  void _onFilterChanged() {
    data.clear();
    _getInitialData();
  }

  @override
  void dispose() {
    PerformanceUtils.disposeResources([
      scrollController,
      sliderPageController,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return directionality(
      child: Scaffold(
        appBar: appbar(
          title: category?.title ?? appText.courses,
          leftIcon: AppAssets.backSvg,
          onTapLeftIcon: () => backRoute(),
          isBasket: true
        ),

        body: RepaintBoundary(
          child: Column(
            children: [
              space(20),

              // Filters section with RepaintBoundary
              RepaintBoundary(
                child: _buildFiltersSection(),
              ),

              space(16),

              // View toggle with RepaintBoundary
              RepaintBoundary(
                child: _buildViewToggle(),
              ),

              space(16),

              // Main content with optimized scrolling
              Expanded(
                child: _buildMainContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Padding(
      padding: padding(),
      child: Row(
        children: [
          // Options filter
          Expanded(
            child: button(
              onTap: () async {
                final res = await baseBottomSheet(
                  child: const OptionsFilter()
                );

                if (res != null && res) {
                  _onFilterChanged();
                }
              },
              width: getSize().width,
              height: 48,
              text: appText.options,
              bgColor: Colors.transparent,
              textColor: const Color(0xFF754FFE),
              borderColor: const Color(0xFF754FFE),
              iconColor: const Color(0xFF754FFE),
              iconPath: AppAssets.optionSvg,
              raduis: 15
            ),
          ),

          space(0, width: 18),

          // Dynamic filters
          Expanded(
            child: button(
              onTap: () async {
                if (filters.isNotEmpty) {
                  final res = await baseBottomSheet(
                    child: const DynamicllyFilter()
                  );

                  if (res != null && res) {
                    _onFilterChanged();
                  }
                }
              },
              width: getSize().width,
              height: 48,
              text: appText.filters,
              bgColor: Colors.transparent,
              textColor: filters.isEmpty 
                  ? const Color(0xFF754FFE).withOpacity(.35) 
                  : const Color(0xFF754FFE),
              borderColor: filters.isEmpty 
                  ? const Color(0xFF754FFE).withOpacity(.35) 
                  : const Color(0xFF754FFE),
              iconColor: filters.isEmpty 
                  ? const Color(0xFF754FFE).withOpacity(.35) 
                  : const Color(0xFF754FFE),
              iconPath: AppAssets.filterSvg,
              raduis: 15
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewToggle() {
    return Padding(
      padding: padding(),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () => setState(() => isGrid = true),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isGrid ? const Color(0xFF754FFE) : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.grid_view,
                color: isGrid ? Colors.white : Colors.grey,
              ),
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: () => setState(() => isGrid = false),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: !isGrid ? const Color(0xFF754FFE) : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.list,
                color: !isGrid ? Colors.white : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    if (data.isEmpty && featuredListData.isEmpty && !isLoading) {
      return emptyState(
        AppAssets.filterEmptyStateSvg,
        appText.dataNotFound,
        appText.dataNotFoundDesc
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (notification) => PerformanceUtils.optimizedScrollNotification(
        notification,
        () {}, // onScrollStart
        () {}, // onScrollUpdate
        () {}, // onScrollEnd
      ),
      child: CustomScrollView(
        controller: scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          // Featured section
          if (featuredListData.isNotEmpty || _isLoadingFeatured)
            SliverToBoxAdapter(
              child: RepaintBoundary(
                child: _buildFeaturedSection(),
              ),
            ),

          // Main course list
          SliverToBoxAdapter(
            child: RepaintBoundary(
              child: _buildCourseList(),
            ),
          ),

          // Loading indicator
          if (isLoading)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Center(child: CircularProgressIndicator()),
              ),
            ),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedSection() {
    if (_isLoadingFeatured) {
      return const SizedBox(
        height: 215,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HomeWidget.titleAndMore(appText.featuredClasses, isViewAll: false),

        SizedBox(
          width: getSize().width,
          height: 215,
          child: PageView.builder(
            controller: sliderPageController,
            onPageChanged: (value) async {
              // Debounce page changes
              PerformanceUtils.debounce(() {
                if (mounted) {
                  setState(() {
                    currentSliderIndex = value;
                  });
                }
              });
            },
            physics: const BouncingScrollPhysics(),
            itemCount: featuredListData.length,
            itemBuilder: (context, index) {
              return RepaintBoundary(
                key: ValueKey('featured_$index'),
                child: courseSliderItem(featuredListData[index]),
              );
            },
          ),
        ),

        space(18),

        // Page indicator
        RepaintBoundary(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(featuredListData.length, (index) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: currentSliderIndex == index ? 16 : 7,
                height: 7,
                margin: padding(horizontal: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFF754FFE),
                  borderRadius: borderRadius()
                ),
              );
            }),
          ),
        ),

        space(14),
      ],
    );
  }

  Widget _buildCourseList() {
    if (isGrid) {
      return PerformanceUtils.buildOptimizedGridView(
        itemCount: (isLoading && data.isEmpty) ? 8 : data.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: TabletDetector.isTablet() ? 3 : 2,
          mainAxisExtent: 190,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16
        ),
        padding: const EdgeInsets.only(
          left: 20,
          right: 20,
          bottom: 40
        ),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          if (isLoading && data.isEmpty) {
            return courseItemShimmer();
          }
          
          return courseItem(
            data[index],
            width: getSize().width / 2,
            endCardPadding: 0.0,
            height: 200.0,
            isShowReward: locator<FilterCourseProvider>().rewardCourse
          );
        },
      );
    } else {
      return PerformanceUtils.buildOptimizedListView(
        itemCount: (isLoading && data.isEmpty) ? 8 : data.length,
        padding: padding(),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          if (isLoading && data.isEmpty) {
            return courseItemVerticallyShimmer();
          }
          
          return courseItemVertically(
            data[index],
            isShowReward: locator<FilterCourseProvider>().rewardCourse
          );
        },
      );
    }
  }
} 