import 'package:flutter/material.dart';
import 'package:webinar/services/notification_service.dart';

class NotificationExamplePage extends StatefulWidget {
  const NotificationExamplePage({Key? key}) : super(key: key);

  @override
  State<NotificationExamplePage> createState() => _NotificationExamplePageState();
}

class _NotificationExamplePageState extends State<NotificationExamplePage> {
  final NotificationService _notificationService = NotificationService();
  String? _fcmToken;

  @override
  void initState() {
    super.initState();
    _loadFCMToken();
  }

  /// تحميل FCM Token
  Future<void> _loadFCMToken() async {
    String? token = await _notificationService.getToken();
    setState(() {
      _fcmToken = token;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال على الإشعارات'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عرض FCM Token
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'FCM Token:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _fcmToken ?? 'جاري التحميل...',
                      style: const TextStyle(fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        if (_fcmToken != null) {
                          // نسخ التوكن إلى الحافظة
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم نسخ التوكن إلى الحافظة'),
                            ),
                          );
                        }
                      },
                      child: const Text('نسخ التوكن'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // أزرار الإشعارات
            const Text(
              'اختبار الإشعارات:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // إشعار بسيط
            ElevatedButton(
              onPressed: () => _sendSimpleNotification(),
              child: const Text('إرسال إشعار بسيط'),
            ),
            
            const SizedBox(height: 8),
            
            // إشعار كورس
            ElevatedButton(
              onPressed: () => _sendCourseNotification(),
              child: const Text('إشعار كورس جديد'),
            ),
            
            const SizedBox(height: 8),
            
            // إشعار مهمة
            ElevatedButton(
              onPressed: () => _sendAssignmentNotification(),
              child: const Text('إشعار مهمة جديدة'),
            ),
            
            const SizedBox(height: 8),
            
            // إشعار مجدول
            ElevatedButton(
              onPressed: () => _scheduleNotification(),
              child: const Text('جدولة إشعار (بعد 10 ثواني)'),
            ),
            
            const SizedBox(height: 20),
            
            // إدارة المواضيع
            const Text(
              'إدارة المواضيع:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _subscribeToTopic('general'),
                    child: const Text('الاشتراك في العام'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _unsubscribeFromTopic('general'),
                    child: const Text('إلغاء الاشتراك'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _subscribeToTopic('courses'),
                    child: const Text('الاشتراك في الكورسات'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _unsubscribeFromTopic('courses'),
                    child: const Text('إلغاء الاشتراك'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // إلغاء الإشعارات
            ElevatedButton(
              onPressed: () => _clearAllNotifications(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('إلغاء جميع الإشعارات'),
            ),
          ],
        ),
      ),
    );
  }

  /// إرسال إشعار بسيط
  Future<void> _sendSimpleNotification() async {
    await _notificationService.sendLocalNotification(
      title: 'إشعار تجريبي',
      body: 'هذا إشعار تجريبي من التطبيق',
    );
    
    _showSnackBar('تم إرسال الإشعار البسيط');
  }

  /// إرسال إشعار كورس
  Future<void> _sendCourseNotification() async {
    await _notificationService.sendLocalNotification(
      title: 'كورس جديد متاح!',
      body: 'تم إضافة كورس جديد في البرمجة',
      data: {
        'type': 'course',
        'course_id': '123',
        'course_name': 'تعلم Flutter',
      },
    );
    
    _showSnackBar('تم إرسال إشعار الكورس');
  }

  /// إرسال إشعار مهمة
  Future<void> _sendAssignmentNotification() async {
    await _notificationService.sendLocalNotification(
      title: 'مهمة جديدة!',
      body: 'لديك مهمة جديدة تحتاج إلى إنجازها',
      data: {
        'type': 'assignment',
        'assignment_id': '456',
        'assignment_name': 'مهمة Flutter',
      },
    );
    
    _showSnackBar('تم إرسال إشعار المهمة');
  }

  /// جدولة إشعار
  Future<void> _scheduleNotification() async {
    DateTime scheduledDate = DateTime.now().add(const Duration(seconds: 10));
    
    await _notificationService.scheduleLocalNotification(
      title: 'إشعار مجدول',
      body: 'هذا إشعار تم جدولته مسبقاً',
      scheduledDate: scheduledDate,
      data: {
        'type': 'scheduled',
        'scheduled_time': scheduledDate.toIso8601String(),
      },
    );
    
    _showSnackBar('تم جدولة الإشعار لبعد 10 ثواني');
  }

  /// الاشتراك في موضوع
  Future<void> _subscribeToTopic(String topic) async {
    await _notificationService.subscribe(topic);
    _showSnackBar('تم الاشتراك في موضوع: $topic');
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> _unsubscribeFromTopic(String topic) async {
    await _notificationService.unsubscribe(topic);
    _showSnackBar('تم إلغاء الاشتراك من موضوع: $topic');
  }

  /// إلغاء جميع الإشعارات
  Future<void> _clearAllNotifications() async {
    await _notificationService.clearAllNotifications();
    _showSnackBar('تم إلغاء جميع الإشعارات');
  }

  /// عرض رسالة
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
} 