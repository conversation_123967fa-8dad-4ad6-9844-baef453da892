import 'package:flutter/material.dart';
import 'package:webinar/common/data/app_language.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/locator.dart';

TextStyle style48Bold() {
  return TextStyle(
    fontFamily: 'Zain-Bold',
    color: grey33,
    fontSize: 48
  );
}
TextStyle style24Bold() => style48Bold().copyWith(fontSize: 24);
TextStyle style22Bold() => style48Bold().copyWith(fontSize: 22);
TextStyle style20Bold() => style48Bold().copyWith(fontSize: 20);
TextStyle style18Bold() => style48Bold().copyWith(fontSize: 18);
TextStyle style16Bold() => style48Bold().copyWith(fontSize: 16);
TextStyle style14Bold() => style48Bold().copyWith(fontSize: 14);
TextStyle style13Bold() => style48Bold().copyWith(fontSize: 13);
TextStyle style12Bold() => style48Bold().copyWith(fontSize: 12);
TextStyle style11Bold() => style48Bold().copyWith(fontSize: 11);
TextStyle style10Bold() => style48Bold().copyWith(fontSize: 10);
TextStyle style9Bold() => style48Bold().copyWith(fontSize: 9);
TextStyle style8Bold() => style48Bold().copyWith(fontSize: 8);



TextStyle style16Regular() {
  return TextStyle(
    fontFamily: 'Zain-Regular',
    color: grey33,
    fontSize: 16
  );
}
TextStyle style14Regular() => style16Regular().copyWith(fontSize: 14);
TextStyle style12Regular() => style16Regular().copyWith(fontSize: 12);
TextStyle style11Regular() => style16Regular().copyWith(fontSize: 11);
TextStyle style10Regular() => style16Regular().copyWith(fontSize: 10);
TextStyle style9Regular() => style16Regular().copyWith(fontSize: 9);
TextStyle style8Regular() => style16Regular().copyWith(fontSize: 8);



