import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

bool isFlutterLocalNotificationsInitialized = false;
late AndroidNotificationChannel channel;
late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;

/// إعداد الإشعارات المحلية و Firebase
Future<void> setupFlutterNotifications() async {
  if (isFlutterLocalNotificationsInitialized) {
    return;
  }

  // إنشاء قناة الإشعارات لنظام Android
  channel = const AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description: 'This channel is used for important notifications.', // description
    importance: Importance.high,
    playSound: true,
    showBadge: true,
    enableVibration: true,
  );

  // إعدادات الإشعارات لنظام Android (بدون أيقونة)
  const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings(
    '@mipmap/ic_launcher'
  );
  
  // إعدادات الإشعارات لنظام iOS
  const DarwinInitializationSettings initializationSettingsDarwin = DarwinInitializationSettings(
    defaultPresentAlert: true,
    defaultPresentSound: true,
    defaultPresentBadge: true,
    requestSoundPermission: true,
    requestBadgePermission: true,
    requestAlertPermission: true,
  );

  // إعدادات الإشعارات العامة
  const InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsDarwin,
  );

  flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  // تهيئة الإشعارات المحلية
  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse details) {
      _handleNotificationTap(details);
    },
  );
  
  // إنشاء قناة الإشعارات لنظام Android
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  // طلب أذونات الإشعارات
  await _requestNotificationPermissions();

  // إعداد Firebase Messaging
  await _setupFirebaseMessaging();
  
  isFlutterLocalNotificationsInitialized = true;
}

/// طلب أذونات الإشعارات
Future<void> _requestNotificationPermissions() async {
  // طلب إذن الإشعارات العام
  await Permission.notification.request();

  // إعداد Firebase Messaging permissions
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  print('إذن الإشعارات: ${settings.authorizationStatus}');
}

/// إعداد Firebase Messaging
Future<void> _setupFirebaseMessaging() async {
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  // الحصول على FCM Token
  String? token = await FirebaseMessaging.instance.getToken();
  print('FCM Token: $token');
  
  // يمكنك إرسال هذا التوكن إلى الخادم الخاص بك
  // await sendTokenToServer(token);
}

/// معالجة النقر على الإشعار
void _handleNotificationTap(NotificationResponse details) {
  if (details.payload != null) {
    try {
      Map<String, dynamic> data = jsonDecode(details.payload!);
      print('تم النقر على الإشعار: $data');
      
      // يمكنك إضافة منطق التنقل هنا حسب نوع الإشعار
      _navigateBasedOnNotification(data);
    } catch (e) {
      print('خطأ في معالجة payload الإشعار: $e');
    }
  }
}

/// التنقل حسب نوع الإشعار
void _navigateBasedOnNotification(Map<String, dynamic> data) {
  // مثال على التنقل حسب نوع الإشعار
  String? type = data['type'];
  String? id = data['id'];
  
  switch (type) {
    case 'course':
      // التنقل إلى صفحة الكورس
      print('التنقل إلى الكورس: $id');
      break;
    case 'message':
      // التنقل إلى صفحة الرسائل
      print('التنقل إلى الرسائل: $id');
      break;
    case 'assignment':
      // التنقل إلى صفحة المهام
      print('التنقل إلى المهمة: $id');
      break;
    default:
      print('نوع إشعار غير معروف: $type');
  }
}

/// عرض الإشعار المحلي
void showFlutterNotification(RemoteMessage message) {
  RemoteNotification? notification = message.notification;
  
  if (notification == null) return;

  // إعدادات الإشعار لنظام Android (بدون أيقونة)
  AndroidNotificationDetails androidNotificationDetails = AndroidNotificationDetails(
    channel.id,
    channel.name,
    channelDescription: channel.description,
    importance: Importance.max,
    priority: Priority.max,
    styleInformation: _getNotificationStyle(notification, message.data),
    enableVibration: true,
    playSound: true,
  );
  
  // إعدادات الإشعار لنظام iOS
  const DarwinNotificationDetails darwinNotificationDetails = DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );
  
  NotificationDetails notificationDetails = NotificationDetails(
    android: androidNotificationDetails, 
    iOS: darwinNotificationDetails,
  );

  // عرض الإشعار
  if (Platform.isAndroid || Platform.isIOS) {
    flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }
}

/// تحديد نمط الإشعار حسب المحتوى
StyleInformation _getNotificationStyle(RemoteNotification notification, Map<String, dynamic> data) {
  // إذا كان هناك صورة في الإشعار
  if (notification.android?.imageUrl != null) {
    return BigPictureStyleInformation(
      FilePathAndroidBitmap(notification.android!.imageUrl!),
      contentTitle: notification.title,
      summaryText: notification.body,
    );
  }
  
  // إذا كان النص طويل
  if (notification.body != null && notification.body!.length > 50) {
    return BigTextStyleInformation(
      notification.body!,
      contentTitle: notification.title,
    );
  }
  
  // النمط الافتراضي
  return const DefaultStyleInformation(true, true);
}

/// إرسال إشعار محلي مخصص
Future<void> showCustomNotification({
  required String title,
  required String body,
  String? payload,
  int? id,
}) async {
  const AndroidNotificationDetails androidNotificationDetails = AndroidNotificationDetails(
    'custom_channel',
    'Custom Notifications',
    channelDescription: 'Custom local notifications',
    importance: Importance.max,
    priority: Priority.max,
  );
  
  const DarwinNotificationDetails darwinNotificationDetails = DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );
  
  const NotificationDetails notificationDetails = NotificationDetails(
    android: androidNotificationDetails,
    iOS: darwinNotificationDetails,
  );

  await flutterLocalNotificationsPlugin.show(
    id ?? DateTime.now().millisecondsSinceEpoch.remainder(100000),
    title,
    body,
    notificationDetails,
    payload: payload,
  );
}

/// جدولة إشعار
Future<void> scheduleNotification({
  required String title,
  required String body,
  required DateTime scheduledDate,
  String? payload,
  int? id,
}) async {
  const AndroidNotificationDetails androidNotificationDetails = AndroidNotificationDetails(
    'scheduled_channel',
    'Scheduled Notifications',
    channelDescription: 'Scheduled notifications',
    importance: Importance.max,
    priority: Priority.max,
  );
  
  const DarwinNotificationDetails darwinNotificationDetails = DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );
  
  const NotificationDetails notificationDetails = NotificationDetails(
    android: androidNotificationDetails,
    iOS: darwinNotificationDetails,
  );

  // تحويل DateTime إلى TZDateTime
  final tz.TZDateTime tzScheduledDate = tz.TZDateTime.from(scheduledDate, tz.local);

  await flutterLocalNotificationsPlugin.zonedSchedule(
    id ?? DateTime.now().millisecondsSinceEpoch.remainder(100000),
    title,
    body,
    tzScheduledDate,
    notificationDetails,
    payload: payload,
    uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  );
}

/// إلغاء إشعار محدد
Future<void> cancelNotification(int id) async {
  await flutterLocalNotificationsPlugin.cancel(id);
}

/// إلغاء جميع الإشعارات
Future<void> cancelAllNotifications() async {
  await flutterLocalNotificationsPlugin.cancelAll();
}

/// الحصول على FCM Token
Future<String?> getFCMToken() async {
  return await FirebaseMessaging.instance.getToken();
}

/// تحديث FCM Token
Future<void> refreshFCMToken() async {
  await FirebaseMessaging.instance.deleteToken();
  String? newToken = await FirebaseMessaging.instance.getToken();
  print('FCM Token الجديد: $newToken');
  // يمكنك إرسال التوكن الجديد إلى الخادم
}

/// الاشتراك في موضوع
Future<void> subscribeToTopic(String topic) async {
  await FirebaseMessaging.instance.subscribeToTopic(topic);
  print('تم الاشتراك في الموضوع: $topic');
}

/// إلغاء الاشتراك من موضوع
Future<void> unsubscribeFromTopic(String topic) async {
  await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
  print('تم إلغاء الاشتراك من الموضوع: $topic');
}
