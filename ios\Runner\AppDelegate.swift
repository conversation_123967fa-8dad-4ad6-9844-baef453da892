import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    
    private var screenSecurityView: UIView?
    
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
        
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        
        // إعداد قناة الاتصال مع Flutter لحماية الشاشة
        let screenSecurityChannel = FlutterMethodChannel(name: "screen_security",
                                                         binaryMessenger: controller.binaryMessenger)
        
        screenSecurityChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "enableScreenSecurity":
                self.enableScreenSecurity()
                result(nil)
            case "disableScreenSecurity":
                self.disableScreenSecurity()
                result(nil)
            case "isScreenSecurityEnabled":
                result(self.isScreenSecurityEnabled())
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        // إعداد قناة كشف وضع المطور الجديدة
        let developerModeChannel = FlutterMethodChannel(name: "developer_mode",
                                                       binaryMessenger: controller.binaryMessenger)
        
        developerModeChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "isDeveloperModeEnabled":
                let isDeveloperMode = self.isDeveloperModeEnabled()
                result(isDeveloperMode)
            case "getDeveloperModeDetails":
                let details = self.getDeveloperModeDetails()
                result(details)
            case "checkSpecificSettings":
                let hasDevSettings = self.checkSpecificDeveloperSettings()
                result(hasDevSettings)
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        // تمكين حماية الشاشة بشكل افتراضي
        enableScreenSecurity()
        
        // إعداد مراقب للتطبيق عند دخوله للخلفية
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
      
      GeneratedPluginRegistrant.register(with: self)
      return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
    
    // MARK: - Screen Security Methods
    
    /// تمكين حماية الشاشة لمنع لقطة الشاشة
    private func enableScreenSecurity() {
        DispatchQueue.main.async {
            // منع لقطة الشاشة عبر إخفاء المحتوى في App Switcher
            if let window = self.window {
                window.isHidden = false
            }
            print("✅ تم تمكين حماية الشاشة لـ iOS")
        }
    }
    
    /// تعطيل حماية الشاشة
    private func disableScreenSecurity() {
        DispatchQueue.main.async {
            self.removeSecurityView()
            print("⚠️ تم تعطيل حماية الشاشة لـ iOS")
        }
    }
    
    /// التحقق من حالة حماية الشاشة
    private func isScreenSecurityEnabled() -> Bool {
        return screenSecurityView != nil
    }
    
    /// إضافة طبقة حماية عند دخول التطبيق للخلفية
    @objc private func applicationWillResignActive() {
        addSecurityView()
    }
    
    /// إزالة طبقة الحماية عند عودة التطبيق للمقدمة
    @objc private func applicationDidBecomeActive() {
        removeSecurityView()
    }
    
    /// إضافة طبقة لإخفاء المحتوى في App Switcher
    private func addSecurityView() {
        guard let window = self.window else { return }
        
        if screenSecurityView == nil {
            screenSecurityView = UIView(frame: window.bounds)
            screenSecurityView?.backgroundColor = UIColor.systemBackground
            
            // إضافة شعار التطبيق
            let logoImageView = UIImageView()
            logoImageView.contentMode = .scaleAspectFit
            logoImageView.translatesAutoresizingMaskIntoConstraints = false
            
            if let logoImage = UIImage(named: "AppIcon") {
                logoImageView.image = logoImage
            }
            
            screenSecurityView?.addSubview(logoImageView)
            
            // تخطيط الشعار في الوسط
            NSLayoutConstraint.activate([
                logoImageView.centerXAnchor.constraint(equalTo: screenSecurityView!.centerXAnchor),
                logoImageView.centerYAnchor.constraint(equalTo: screenSecurityView!.centerYAnchor),
                logoImageView.widthAnchor.constraint(equalToConstant: 120),
                logoImageView.heightAnchor.constraint(equalToConstant: 120)
            ])
            
            window.addSubview(screenSecurityView!)
            screenSecurityView?.translatesAutoresizingMaskIntoConstraints = false
            
            NSLayoutConstraint.activate([
                screenSecurityView!.topAnchor.constraint(equalTo: window.topAnchor),
                screenSecurityView!.leadingAnchor.constraint(equalTo: window.leadingAnchor),
                screenSecurityView!.trailingAnchor.constraint(equalTo: window.trailingAnchor),
                screenSecurityView!.bottomAnchor.constraint(equalTo: window.bottomAnchor)
            ])
        }
    }
    
    /// إزالة طبقة الحماية
    private func removeSecurityView() {
        screenSecurityView?.removeFromSuperview()
        screenSecurityView = nil
    }
    
    // MARK: - Developer Mode Detection Methods
    
    /// التحقق من تفعيل وضع المطور على iOS
    /// يتحقق من وجود أدوات التطوير أو إعدادات مشبوهة
    private func isDeveloperModeEnabled() -> Bool {
        var isDeveloperMode = false
        
        // فحص إذا كان التطبيق يعمل في Simulator
        #if targetEnvironment(simulator)
            isDeveloperMode = true
            print("🚨 iOS Developer Mode: تم اكتشاف Simulator")
        #endif
        
        // فحص إذا كان التطبيق موقع من مطور معروف
        if isAppSignedByKnownDeveloper() {
            isDeveloperMode = true
            print("🚨 iOS Developer Mode: تم اكتشاف توقيع مطور")
        }
        
        // فحص إذا كان الجهاز مكسور الحماية (Jailbroken)
        if isDeviceJailbroken() {
            isDeveloperMode = true
            print("🚨 iOS Developer Mode: تم اكتشاف Jailbreak")
        }
        
        // فحص أدوات التطوير
        if hasDebuggerAttached() {
            isDeveloperMode = true
            print("🚨 iOS Developer Mode: تم اكتشاف Debugger")
        }
        
        if isDeveloperMode {
            print("⚠️ تم اكتشاف وضع المطور على iOS!")
        } else {
            print("✅ وضع المطور غير مفعل على iOS")
        }
        
        return isDeveloperMode
    }
    
    /// الحصول على تفاصيل شاملة عن وضع المطور
    private func getDeveloperModeDetails() -> [String: Any] {
        var details: [String: Any] = [:]
        
        details["platform"] = "iOS"
        details["systemVersion"] = UIDevice.current.systemVersion
        details["deviceModel"] = UIDevice.current.model
        details["deviceName"] = UIDevice.current.name
        details["checkTime"] = Date().timeIntervalSince1970 * 1000
        
        #if targetEnvironment(simulator)
            details["isSimulator"] = true
        #else
            details["isSimulator"] = false
        #endif
        
        details["isJailbroken"] = isDeviceJailbroken()
        details["hasDebugger"] = hasDebuggerAttached()
        details["isSignedByDeveloper"] = isAppSignedByKnownDeveloper()
        
        let isDeveloperMode = details["isSimulator"] as! Bool ||
                             details["isJailbroken"] as! Bool ||
                             details["hasDebugger"] as! Bool ||
                             details["isSignedByDeveloper"] as! Bool
        
        details["isDeveloperMode"] = isDeveloperMode
        
        print("تفاصيل وضع المطور iOS: \(details)")
        
        return details
    }
    
    /// فحص إعدادات محددة تشير إلى تطوير
    private func checkSpecificDeveloperSettings() -> Bool {
        // على iOS، نفحص مؤشرات التطوير
        return isDeviceJailbroken() || hasDebuggerAttached()
    }
    
    /// التحقق من كسر حماية الجهاز (Jailbreak)
    private func isDeviceJailbroken() -> Bool {
        // قائمة المسارات المشبوهة
        let jailbreakPaths = [
            "/Applications/Cydia.app",
            "/Library/MobileSubstrate/MobileSubstrate.dylib",
            "/bin/bash",
            "/usr/sbin/sshd",
            "/etc/apt",
            "/private/var/lib/apt/",
            "/Applications/RockApp.app",
            "/Applications/Icy.app",
            "/Applications/WinterBoard.app",
            "/Applications/SBSettings.app",
            "/Applications/MxTube.app",
            "/Applications/IntelliScreen.app",
            "/Applications/FakeCarrier.app",
            "/Applications/blackra1n.app",
            "/private/var/tmp/cydia.log",
            "/System/Library/LaunchDaemons/com.ikey.bbot.plist"
        ]
        
        for path in jailbreakPaths {
            if FileManager.default.fileExists(atPath: path) {
                return true
            }
        }
        
        // فحص إضافي عبر فتح URL مشبوه
        if let url = URL(string: "cydia://package/com.example.package") {
            if UIApplication.shared.canOpenURL(url) {
                return true
            }
        }
        
        return false
    }
    
    /// فحص ما إذا كان هناك Debugger متصل
    private func hasDebuggerAttached() -> Bool {
        var info = kinfo_proc()
        var mib : [Int32] = [CTL_KERN, KERN_PROC, KERN_PROC_PID, getpid()]
        var size = MemoryLayout<kinfo_proc>.stride
        let junk = sysctl(&mib, UInt32(mib.count), &info, &size, nil, 0)
        assert(junk == 0, "sysctl failed")
        return (info.kp_proc.p_flag & P_TRACED) != 0
    }
    
    /// فحص ما إذا كان التطبيق موقع من مطور معروف
    private func isAppSignedByKnownDeveloper() -> Bool {
        // فحص Bundle Signature
        guard let bundlePath = Bundle.main.bundlePath else { return false }
        
        // في بيئة التطوير، عادة ما يكون هناك embedded.mobileprovision
        let provisioningPath = "\(bundlePath)/embedded.mobileprovision"
        
        return FileManager.default.fileExists(atPath: provisioningPath)
    }
}
