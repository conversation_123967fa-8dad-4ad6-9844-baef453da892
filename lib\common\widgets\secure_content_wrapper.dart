import 'package:flutter/material.dart';
import 'package:webinar/services/screen_security_service.dart';

/// Widget لحماية المحتوى الحساس من لقطة الشاشة والتسجيل
class SecureContentWrapper extends StatefulWidget {
  final Widget child;
  final bool enableSecurity;
  final String? warningMessage;
  final bool enableRecordingDetection;
  
  const SecureContentWrapper({
    Key? key,
    required this.child,
    this.enableSecurity = true,
    this.warningMessage,
    this.enableRecordingDetection = true,
  }) : super(key: key);

  @override
  State<SecureContentWrapper> createState() => _SecureContentWrapperState();
}

class _SecureContentWrapperState extends State<SecureContentWrapper>
    with WidgetsBindingObserver {
  bool _isRecordingDetected = false;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    if (widget.enableSecurity) {
      _enableMaximumSecurity();
    }
    
    if (widget.enableRecordingDetection) {
      _startRecordingMonitoring();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    if (widget.enableSecurity) {
      switch (state) {
        case AppLifecycleState.resumed:
          _enableMaximumSecurity();
          break;
        case AppLifecycleState.paused:
        case AppLifecycleState.inactive:
          // الحفاظ على الحماية عند الخروج من التطبيق
          break;
        case AppLifecycleState.detached:
          break;
        case AppLifecycleState.hidden:
          break;
      }
    }
  }

  Future<void> _enableMaximumSecurity() async {
    try {
      await ScreenSecurityService.enableMaximumSecurity();
    } catch (e) {
      debugPrint('خطأ في تمكين الحماية القصوى: $e');
    }
  }

  Future<void> _startRecordingMonitoring() async {
    try {
      await ScreenSecurityService.startRecordingMonitoring();
      
      // مراقبة مستمرة لحالة التسجيل
      Stream.periodic(const Duration(seconds: 3)).listen((_) async {
        if (mounted) {
          bool isRecording = ScreenSecurityService.isRecordingDetected;
          if (isRecording != _isRecordingDetected) {
            setState(() {
              _isRecordingDetected = isRecording;
            });
          }
        }
      });
    } catch (e) {
      debugPrint('خطأ في بدء مراقبة التسجيل: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        
        // عرض تحذير إذا تم تمرير رسالة
        if (widget.warningMessage != null)
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.9),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.security,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.warningMessage!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
        // تحذير عند اكتشاف تسجيل الشاشة
        if (_isRecordingDetected && widget.enableRecordingDetection)
          Positioned(
            top: MediaQuery.of(context).padding.top + 60,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.9),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.videocam_off,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '🚨 تم اكتشاف محاولة تسجيل - المحتوى محمي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

/// Widget مخصص للصفحات التي تحتوي على محتوى حساس
class SecurePage extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool showSecurityIndicator;
  
  const SecurePage({
    Key? key,
    required this.child,
    this.title,
    this.showSecurityIndicator = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SecureContentWrapper(
      enableSecurity: true,
      warningMessage: showSecurityIndicator 
          ? "المحتوى محمي - لا يمكن أخذ لقطة شاشة"
          : null,
      child: child,
    );
  }
}

/// Mixin للصفحات التي تحتاج حماية من لقطة الشاشة
mixin SecurePageMixin<T extends StatefulWidget> on State<T> {
  
  @override
  void initState() {
    super.initState();
    _enablePageSecurity();
  }
  
  @override
  void dispose() {
    _disablePageSecurity();
    super.dispose();
  }
  
  Future<void> _enablePageSecurity() async {
    await ScreenSecurityService.protectCurrentScreen();
  }
  
  Future<void> _disablePageSecurity() async {
    // يمكن تركها مفعلة أو تعطيلها حسب الحاجة
    // await ScreenSecurityService.unprotectCurrentScreen();
  }
} 