import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:webinar/app/pages/main_page/key_app_page/libraries_page/libraries_page.dart';
import 'package:webinar/app/pages/main_page/key_app_page/subscription_guide_page.dart';
import 'package:webinar/app/pages/main_page/home_page/support_message_page/support_message_page.dart';
import 'package:webinar/app/providers/app_language_provider.dart';
import 'package:webinar/app/providers/drawer_provider.dart';
import 'package:webinar/app/providers/user_provider.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/components.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';
import 'package:webinar/locator.dart';
import 'package:webinar/common/enums/error_enum.dart';
import 'package:webinar/common/data/app_data.dart';
import 'package:webinar/services/crisp_chat_service.dart';

import '../../../../common/utils/object_instance.dart';
import 'dart:async';

class KeyAppPage extends StatefulWidget {
  const KeyAppPage({super.key});

  @override
  State<KeyAppPage> createState() => _KeyAppPageState();
}

class _KeyAppPageState extends State<KeyAppPage> {
  final TextEditingController keyController = TextEditingController();
  final FocusNode keyFocusNode = FocusNode();

  // Define the purple color
  final Color purpleColor = const Color(0xFF754FFE);
  final Color orangeColor = const Color(0xFFf3770d);

  // Overlay entry for top notification
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTopNotification();
    keyController.dispose();
    keyFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppLanguageProvider>(
      builder: (context, appLanguageProvider, _) {
        return directionality(
          child: Consumer<DrawerProvider>(
            builder: (context, drawerProvider, _) {
              return ClipRRect(
                borderRadius: borderRadius(radius: drawerProvider.isOpenDrawer ? 20 : 0),
                child: Scaffold(
                  backgroundColor: greyFA,
                  appBar: appbar(
                    title: appText.keyApp,
                    leftIcon: AppAssets.menuSvg,
                    onTapLeftIcon: () {
                      drawerController.showDrawer();
                    },
                  ),
                  body: SingleChildScrollView(
                    padding: padding(horizontal: 20, vertical: 30),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Key illustration with purple colors #754FFE
                        Container(
                          width: getSize().width * 0.8,
                          height: 200,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                purpleColor.withOpacity(0.3),
                                purpleColor.withOpacity(0.1),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: borderRadius(radius: 20),
                          ),
                          child: Stack(
                            children: [
                              // Decorative dots
                              Positioned(
                                top: 20,
                                left: 30,
                                child: Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: List.generate(15, (index) => Container(
                                    width: 4,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color: purpleColor.withOpacity(0.6),
                                      shape: BoxShape.circle,
                                    ),
                                  )),
                                ),
                              ),
                              // Key card
                              Center(
                                child: Container(
                                  width: 200,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        purpleColor,
                                        purpleColor.withOpacity(0.8),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: borderRadius(radius: 12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      // Key icon
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: Colors.yellow.shade600,
                                          borderRadius: borderRadius(radius: 8),
                                        ),
                                        child: Icon(
                                          Icons.vpn_key,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                      space(12),
                                      // Key placeholder
                                      Container(
                                        width: 150,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: borderRadius(radius: 4),
                                        ),
                                        child: Center(
                                          child: Text(
                                            appText.subscriptionKeyPlaceholder,
                                            style: style12Regular().copyWith(
                                              color: greyB2,
                                              letterSpacing: 2,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // Arrow
                              Positioned(
                                bottom: 30,
                                right: 40,
                                child: Container(
                                  width: 60,
                                  height: 30,
                                  child: CustomPaint(
                                    painter: ArrowPainter(color: purpleColor),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        space(40),

                        // Enter key text
                        Text(
                          appText.enterSubscriptionKey,
                          style: style18Bold(),
                          textAlign: TextAlign.center,
                        ),

                        space(8),

                        // Key description
                        Text(
                          appText.keyConsistsOfLettersAndNumbers,
                          style: style14Regular().copyWith(color: greyB2),
                          textAlign: TextAlign.center,
                        ),

                        space(30),

                        // Key input field
                        input(
                          keyController,
                          keyFocusNode,
                          appText.subscriptionKeyPlaceholder,
                          isCenter: true,
                          fontSize: 16,
                          letterSpacing: 2,
                        ),

                        space(30),

                        // Login button
                        button(
                          onTap: () {
                            // Handle key validation and login
                            _handleKeyLogin();
                          },
                          width: getSize().width,
                          height: 52,
                          text: appText.login,
                          bgColor: green77(),
                          textColor: Colors.white,
                          raduis: 12,
                        ),

                        space(50),

                        // How to subscribe section - moved above "no key" section
                        Text(
                          'معرفة طريقة الاشتراك',
                          style: style16Bold(),
                          textAlign: TextAlign.center,
                        ),

                        space(15),

                        // Two buttons side by side
                        Row(
                          children: [
                            // Subscribe guide button (Orange)
                            Expanded(
                              child: button(
                                onTap: () {
                                  // Navigate to subscription guide page
                                  _navigateToSubscriptionGuide();
                                },
                                width: (getSize().width - 40 - 12) / 2, // Half width minus padding and spacing
                                height: 52,
                                text: 'شرح كيفية الاشتراك',
                                bgColor: orangeColor,
                                textColor: Colors.white,
                                raduis: 12,
                              ),
                            ),
                            
                            space(0, width: 12), // Space between buttons
                            
                            // Direct support button (Purple)
                            Expanded(
                              child: button(
                                onTap: () {
                                  // Navigate to support page with live chat tab opened
                                  _navigateToDirectSupport();
                                },
                                width: (getSize().width - 40 - 12) / 2, // Half width minus padding and spacing
                                height: 52,
                                text: 'تواصل مع الدعم المباشر',
                                bgColor: purpleColor, // #754FFE
                                textColor: Colors.white,
                                raduis: 12,
                              ),
                            ),
                          ],
                        ),

                        space(30),

                        // No key section
                        Container(
                          width: getSize().width,
                          padding: padding(horizontal: 20, vertical: 20),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: borderRadius(radius: 16),
                            border: Border.all(color: Colors.orange.shade200),
                          ),
                          child: Column(
                            children: [
                              Text(
                                appText.noKeyYet,
                                style: style16Bold().copyWith(
                                  color: Colors.orange.shade700,
                                ),
                                textAlign: TextAlign.center,
                              ),

                              space(12),

                              Text(
                                appText.buyKeyFromNearestLibrary,
                                style: style14Regular().copyWith(
                                  color: Colors.orange.shade600,
                                ),
                                textAlign: TextAlign.center,
                              ),

                              space(20),

                              // Show locations button
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    color: Colors.orange.shade600,
                                    size: 20,
                                  ),
                                  space(0, width: 8),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        nextRoute(LibrariesPage.pageName);
                                      },
                                      behavior: HitTestBehavior.opaque,
                                      child: Container(
                                        padding: padding(vertical: 12, horizontal: 16),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.shade100,
                                          borderRadius: borderRadius(radius: 8),
                                          border: Border.all(color: Colors.orange.shade300),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              appText.showLocations,
                                              style: style14Bold().copyWith(
                                                color: Colors.orange.shade700,
                                              ),
                                            ),
                                            Icon(
                                              Icons.arrow_forward_ios,
                                              color: Colors.orange.shade700,
                                              size: 16,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        space(30),

                        // Extra space at bottom to prevent bottom navigation overlap
                        space(100),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _handleKeyLogin() async {
    final key = keyController.text.trim();
    if (key.isEmpty) {
      _showTopNotification("ادخل مفتاح الاشتراك", isError: true);
      return;
    }

    // Check if user is logged in
    bool isLoggedIn = await _checkUserLoginAsync();
    if (!isLoggedIn) {
      _showLoginRequiredDialog();
      return;
    }

    // TODO: Implement key validation logic
    // For now, simulate validation - you can replace this with actual API call
    if (key.length < 8) {
      // If key is too short, consider it invalid
      _showTopNotification("ادخل مفتاح الاشتراك", isError: true);
    } else {
      // If key meets criteria, consider it valid
      _showTopNotification("تم تحقق من مفتاح بنجاح", isError: false);
    }
  }

  bool _isUserLoggedIn() {
    // Check if user is logged in by checking access token
    // This is a synchronous check, for async check use the method below
    return locator<UserProvider>().profile != null;
  }

  Future<bool> _checkUserLoginAsync() async {
    try {
      String token = await AppData.getAccessToken();
      return token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'مطلوب تسجيل دخول',
            style: style16Bold(),
            textAlign: TextAlign.center,
          ),
          content: Text(
            'يجب عليك تسجيل الدخول أو إنشاء حساب جديد لتتمكن من إدخال مفتاح الاشتراك',
            style: style14Regular(),
            textAlign: TextAlign.center,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius(radius: 12),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Navigate to register page
                      nextRoute('/register');
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: orangeColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: borderRadius(radius: 8),
                      ),
                    ),
                    child: Text(
                      'إنشاء حساب',
                      style: style14Bold().copyWith(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Navigate to login page
                      nextRoute('/login');
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: purpleColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: borderRadius(radius: 8),
                      ),
                    ),
                    child: Text(
                      'تسجيل دخول',
                      style: style14Bold().copyWith(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  void _navigateToSubscriptionGuide() {
    nextRoute(SubscriptionGuidePage.pageName);
  }

  void _navigateToDirectSupport() {
    // Navigate to support message page with live chat tab (tab index 2)
    nextRoute(SupportMessagePage.pageName, arguments: {'initialTab': 2});
  }

  void _showTopNotification(String message, {required bool isError}) {
    // Remove any existing overlay
    _removeTopNotification();
    
    OverlayState? overlayState = Overlay.of(context);
    if (overlayState == null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => TweenAnimationBuilder(
        duration: Duration(milliseconds: 500),
        tween: Tween<double>(begin: -100, end: 10),
        builder: (context, double offset, child) {
          return Positioned(
            top: MediaQuery.of(context).viewPadding.top + offset,
            left: 20,
            right: 20,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isError ? red49 : green77(),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 15,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isError ? Icons.error_outline : Icons.check_circle_outline,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        message,
                        style: style14Bold().copyWith(color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );

    overlayState.insert(_overlayEntry!);

    // Auto-remove after 3 seconds with fade-out animation
    Timer(Duration(seconds: 3), () {
      _removeTopNotification();
    });
  }

  void _removeTopNotification() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class ArrowPainter extends CustomPainter {
  final Color color;
  
  ArrowPainter({this.color = Colors.orange});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.7)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    path.moveTo(0, size.height / 2);
    path.lineTo(size.width * 0.7, size.height / 2);
    path.lineTo(size.width * 0.5, size.height * 0.2);
    path.moveTo(size.width * 0.7, size.height / 2);
    path.lineTo(size.width * 0.5, size.height * 0.8);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 