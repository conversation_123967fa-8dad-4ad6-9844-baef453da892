# 🧪 دليل اختبار الإشعارات السريع

## ✅ التحقق من الإعداد الأساسي

### 1. تأكد من ظهور هذه الرسائل في الـ logs:
```
✅ إذن الإشعارات: AuthorizationStatus.authorized
✅ FCM Token: eBp7GP-rQ8u0CzKPqTfAd7:APA91bEy...
✅ FlutterFirebaseMessagingBackgroundService started!
```

## 🔧 اختبار الإشعارات المحلية

### في التطبيق، اذهب للصفحة الرئيسية وستجد قسم "اختبار الإشعارات":

1. **اضغط "اختبار إشعار محلي"**
   - ✅ يجب أن يظهر إشعار فوراً
   - ✅ يجب أن تسمع صوت الإشعار
   - ✅ يجب أن يظهر في شريط الإشعارات

2. **اضغط "اختبار إشعار مع بيانات"**
   - ✅ يجب أن يظهر إشعار
   - ✅ عند النقر عليه، يجب أن يطبع في الـ logs معلومات التنقل

3. **اضغط "اختبار إشعار مجدول (5 ثواني)"**
   - ✅ انتظر 5 ثواني
   - ✅ يجب أن يظهر إشعار مجدول

4. **اضغط "عرض FCM Token"**
   - ✅ يجب أن يظهر التوكن في نافذة منبثقة
   - ✅ انسخ التوكن للاختبار التالي

## 🌐 اختبار إشعارات Firebase (من الخادم)

### 1. احصل على Server Key:
- اذهب إلى [Firebase Console](https://console.firebase.google.com)
- اختر مشروعك
- اذهب إلى **Project Settings** ⚙️
- تبويب **Cloud Messaging**
- انسخ **Server Key**

### 2. استخدم أداة الاختبار:
- افتح ملف `test_fcm_sender.html` في المتصفح
- املأ البيانات:
  - **Server Key**: من Firebase Console
  - **FCM Token**: من التطبيق
  - **العنوان والمحتوى**: حسب رغبتك
- اضغط **"إرسال الإشعار"**

### 3. اختبار حالات مختلفة:
- **التطبيق مفتوح**: يجب أن يظهر الإشعار
- **التطبيق في الخلفية**: يجب أن يظهر في شريط الإشعارات
- **التطبيق مغلق**: يجب أن يظهر في شريط الإشعارات

## 🔍 استكشاف الأخطاء

### إذا لم تظهر الإشعارات المحلية:
1. تأكد من أذونات الإشعارات في إعدادات الهاتف
2. تحقق من أن التطبيق غير مكتوم في إعدادات الإشعارات
3. أعد تشغيل التطبيق

### إذا لم تظهر إشعارات Firebase:
1. تأكد من صحة Server Key
2. تأكد من صحة FCM Token
3. تحقق من اتصال الإنترنت
4. تأكد من أن Firebase مُعد بشكل صحيح

### إذا لم يعمل التنقل عند النقر:
1. تحقق من الـ logs للتأكد من استلام البيانات
2. تأكد من أن التطبيق يستمع لأحداث النقر
3. تحقق من أن البيانات تحتوي على `type` صحيح

## 📱 FCM Token الحالي
```
eBp7GP-rQ8u0CzKPqTfAd7:APA91bEyJLqyqenXtfkDe29zP_jRhi7g1rS2-xNiThpc6BxRAoojfoSqC_Uxs7-RmAvjboUsjqvHs-TSEDajbSnbewRmVIbsyNteHFzZBFlygTRQ3XfueeI
```

## 🎯 أنواع الإشعارات المدعومة
- `course` - كورس
- `assignment` - مهمة  
- `quiz` - اختبار
- `meeting` - اجتماع
- `message` - رسالة
- `certificate` - شهادة
- `general` - عام

---

**ملاحظة**: إذا واجهت أي مشاكل، تحقق من الـ logs في Flutter وتأكد من أن جميع التبعيات مثبتة بشكل صحيح. 