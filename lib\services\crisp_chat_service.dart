import 'package:crisp_chat/crisp_chat.dart';
import 'package:flutter/material.dart';
import 'package:webinar/common/data/app_data.dart';

class CrispChatService {
  static const String _websiteId = 'b1536c55-ec46-4cfb-9826-bb0a37cc6711';
  static CrispConfig? _config;
  
  /// تهيئة Crisp Chat
  static Future<void> initialize() async {
    try {
      // إنشاء التكوين الأساسي
      _config = CrispConfig(
        websiteID: _websiteId,
      );
      print('✅ تم تهيئة Crisp Chat بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة Crisp Chat: $e');
    }
  }
  
  /// فتح نافذة الدردشة
  static Future<void> openChat(BuildContext context) async {
    try {
      // تحديث معلومات المستخدم قبل فتح الدردشة
      await _updateUserInfo();
      
      // فتح الدردشة
      if (_config != null) {
        await FlutterCrispChat.openCrispChat(config: _config!);
        print('✅ تم فتح Crisp Chat');
      } else {
        throw Exception('Crisp Chat غير مهيأ');
      }
    } catch (e) {
      print('❌ خطأ في فتح Crisp Chat: $e');
      _showErrorDialog(context, 'خطأ في فتح الدردشة المباشرة');
    }
  }
  
  /// تحديث معلومات المستخدم
  static Future<void> _updateUserInfo() async {
    try {
      // الحصول على معلومات المستخدم من التطبيق
      String userName = await AppData.getName();
      String? userEmail = await AppData.getEmail();
      String? userPhone = await AppData.getPhone();
      
      // إنشاء تكوين محدث مع معلومات المستخدم
      if (userName.isNotEmpty || (userEmail != null && userEmail.isNotEmpty) || (userPhone != null && userPhone.isNotEmpty)) {
        _config = CrispConfig(
          websiteID: _websiteId,
          user: User(
            nickName: userName.isNotEmpty ? userName : null,
            email: (userEmail != null && userEmail.isNotEmpty) ? userEmail : null,
            phone: (userPhone != null && userPhone.isNotEmpty) ? userPhone : null,
          ),
        );
      } else {
        // إذا لم تكن هناك معلومات مستخدم، استخدم التكوين الأساسي
        _config = CrispConfig(
          websiteID: _websiteId,
        );
      }
      
      print('✅ تم تحديث معلومات المستخدم في Crisp Chat');
    } catch (e) {
      print('❌ خطأ في تحديث معلومات المستخدم: $e');
    }
  }
  
  /// إعادة تعيين الدردشة
  static Future<void> resetChat() async {
    try {
      await FlutterCrispChat.resetCrispChatSession();
      print('✅ تم إعادة تعيين جلسة Crisp Chat');
    } catch (e) {
      print('❌ خطأ في إعادة تعيين الدردشة: $e');
    }
  }
  
  /// الحصول على معرف الجلسة
  static Future<String?> getSessionId() async {
    try {
      String? sessionId = await FlutterCrispChat.getSessionIdentifier();
      return sessionId;
    } catch (e) {
      print('❌ خطأ في الحصول على معرف الجلسة: $e');
      return null;
    }
  }
  
  /// عرض رسالة خطأ
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }
  
  /// التحقق من توفر الدردشة
  static Future<bool> isChatAvailable() async {
    try {
      // يمكن إضافة منطق للتحقق من توفر الدعم
      return _config != null;
    } catch (e) {
      print('❌ خطأ في التحقق من توفر الدردشة: $e');
      return false;
    }
  }
} 