class LibraryModel {
  final int id;
  final String name;
  final String phone;
  final String address;
  final String governorate;
  final double? latitude;
  final double? longitude;

  LibraryModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.address,
    required this.governorate,
    this.latitude,
    this.longitude,
  });

  factory LibraryModel.fromJson(Map<String, dynamic> json) {
    return LibraryModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      governorate: json['governorate'] ?? '',
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'address': address,
      'governorate': governorate,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
} 