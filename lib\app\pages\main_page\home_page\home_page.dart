import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webinar/app/pages/main_page/categories_page/filter_category_page/filter_category_page.dart';
import 'package:webinar/app/providers/drawer_provider.dart';
import 'package:webinar/app/providers/page_provider.dart';
import 'package:webinar/app/services/guest_service/course_service.dart';
import 'package:webinar/app/services/user_service/user_service.dart';
import 'package:webinar/app/widgets/main_widget/home_widget/home_widget.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/data/app_data.dart';
import 'package:webinar/common/enums/page_name_enum.dart';
import 'package:webinar/common/shimmer_component.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/common/utils/currency_utils.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';
import 'package:webinar/app/pages/main_page/home_page/support_message_page/support_message_page.dart';
import '../../../../locator.dart';
import '../../../models/course_model.dart';
import '../../../providers/app_language_provider.dart';
import '../../../../common/components.dart';
import '../../../providers/filter_course_provider.dart';
import '../../video_player_page.dart';

// رسام الموجة للتأثيرات الزخرفية
class WavePainter extends CustomPainter {
  final Color color;
  
  WavePainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
      
    var path = Path();
    path.moveTo(0, size.height);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.5,
      size.width * 0.5,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 1.1,
      size.width,
      size.height * 0.8,
    );
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin{

  String token = '';
  String name = '';

  TextEditingController searchController = TextEditingController();
  FocusNode searchNode = FocusNode();

  late AnimationController appBarController;
  late Animation<double> appBarAnimation;

  double appBarHeight = 230;

  ScrollController scrollController = ScrollController();

  PageController sliderPageController = PageController();
  int currentSliderIndex = 0;
  
  PageController adSliderPageController = PageController();
  int currentAdSliderIndex = 0;

  
  
  bool isLoadingFeaturedListData=false;
  List<CourseModel> featuredListData = [];

  bool isLoadingNewsetListData=false;
  List<CourseModel> newsetListData = [];
  
  bool isLoadingBestRatedListData=false;
  List<CourseModel> bestRatedListData = [];
  
  bool isLoadingBestSellingListData=false;
  List<CourseModel> bestSellingListData = [];
  
  bool isLoadingDiscountListData=true;
  List<CourseModel> discountListData = [];
  
  bool isLoadingFreeListData=true;
  List<CourseModel> freeListData = [];
  
  bool isLoadingBundleData=false;
  List<CourseModel> bundleData = [];

  // متغيرات قسم الدورات المتاحة حالياً
  bool isLoadingAvailableCoursesData = true; // يبدأ بـ true ويتم تغييره في getData
  List<CourseModel> availableCoursesData = [];

  // متغيرات نسبة الخصم الديناميكية
  bool isLoadingDiscountPercent = false;
  int generalDiscountPercent = 50; // قيمة افتراضية

  @override
  void initState() {
    super.initState();

    getToken();

    appBarController = AnimationController(vsync: this,duration: const Duration(milliseconds: 200));
    appBarAnimation = Tween<double>(
      begin: 150 + MediaQuery.of(navigatorKey.currentContext!).viewPadding.top, 
      end: 80 + MediaQuery.of(navigatorKey.currentContext!).viewPadding.top, 
    ).animate(appBarController);

    scrollController.addListener(() {

      if(scrollController.position.pixels > 100){

        if(!appBarController.isAnimating){
          if(appBarController.status == AnimationStatus.dismissed){
            appBarController.forward();
          }
        }
      }else if(scrollController.position.pixels < 50){
        
        if(!appBarController.isAnimating){
          if(appBarController.status == AnimationStatus.completed){
            appBarController.reverse();
          }
        }

      }
    });


    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {

      if(ModalRoute.of(context)!.settings.arguments != null){ 
        if(AppData.canShowFinalizeSheet){
          
          AppData.canShowFinalizeSheet = false;

          // finalize signup
          HomeWidget.showFinalizeRegister( (ModalRoute.of(context)!.settings.arguments as int) ).then((value) {
            if(value){
              getToken();
            }
          });

        }
      }

      getData();
    });

  }


  getData() async {

    await locator<CurrencyUtils>().init();

    isLoadingFeaturedListData = true;
    isLoadingNewsetListData = true;
    isLoadingBundleData=true;
    isLoadingBestRatedListData=true;
    isLoadingBestSellingListData=true;
    isLoadingDiscountListData=true;
    isLoadingFreeListData=true;
    
    // تحميل الدورات المتاحة حالياً
    isLoadingAvailableCoursesData = true;
    
    CourseService.featuredCourse().then((value) {
      if (mounted) {
        setState(() {
          isLoadingFeaturedListData = false;
          featuredListData = value;
        });
      }
    });

    CourseService.getAll(offset: 0, bundle: true).then((value) {
      if (mounted) {
        setState(() {
          isLoadingBundleData=false;
          bundleData = value;
        });
      }
    });

    CourseService.getAll(offset: 0, sort: 'newest').then((value) {
      if (mounted) {
        setState(() {
          isLoadingNewsetListData=false;
          newsetListData = value;
        });
      }
    });

    CourseService.getAll(offset: 0, sort: 'best_rates').then((value) {
      if (mounted) {
        setState(() {
          isLoadingBestRatedListData = false;
          bestRatedListData = value;
        });
      }
    });

    
    CourseService.getAll(offset: 0, sort: 'bestsellers').then((value) {
      if (mounted) {
        setState(() {
          isLoadingBestSellingListData = false;
          bestSellingListData = value;
        });
      }
    });
    
    CourseService.getAll(offset: 0, discount: true).then((value) {
      if (mounted) {
        setState(() {
          isLoadingDiscountListData = false;
          discountListData = value;
        });
      }
    });
    
    CourseService.getAll(offset: 0, free: true).then((value) {
      if (mounted) {
        setState(() {
          isLoadingFreeListData = false;
          freeListData = value;
        });
      }
    });

    // جلب الدورات المتاحة حالياً (أحدث الدورات غير المجانية والمفعلة)
    CourseService.getAll(offset: 0, sort: 'newest').then((value) {
      // فلترة الدورات المتاحة فقط (غير منتهية الصلاحية وليست مجانية)
      List<CourseModel> availableCourses = value.where((course) {
        return course.status != 'inactive' && 
               course.price != null && 
               course.price > 0 &&
               course.expired != true;
      }).take(10).toList();
      
      if (mounted) {
        setState(() {
          isLoadingAvailableCoursesData = false;
          availableCoursesData = availableCourses;
        });
      }
    });

    // متغيرات نسبة الخصم الديناميكية
    isLoadingDiscountPercent = true;
    generalDiscountPercent = 50; // قيمة افتراضية

    // جلب نسبة الخصم بعد تحميل الدورات المتاحة
    CourseService.getGeneralDiscountPercent().then((value) {
      if (mounted) {
        setState(() {
          isLoadingDiscountPercent = false;
          generalDiscountPercent = value;
        });
      }
    });
  }


  getToken()async{

    AppData.getAccessToken().then((value) {
      setState(() {
        token = value;
      });

      if(token.isNotEmpty){
        
        // get profile and save naem
        UserService.getProfile().then((value) async {
          if(value != null){            
            await AppData.saveName(value.fullName ?? '');
            getUserName();
          }
        });
        
      }
    });
    
    getUserName();
    
  }

  getUserName(){

    AppData.getName().then((value) {
      setState(() {
        name = value;
      });
    });
  }

  // دالة تشغيل الفيديو التعريفي
  void _playIntroVideo() async {
    try {
      // فتح صفحة تشغيل الفيديو المحلي
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => VideoPlayerPage(),
          fullscreenDialog: true,
        ),
      );
    } catch (e) {
      // في حالة حدوث خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء محاولة تشغيل الفيديو'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Widget للصورة الرئيسية الجذابة
  Widget _buildHeroBanner() {
    // الحصول على عرض الشاشة لجعل التصميم متجاوب
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    
    // تحديد الأحجام بناءً على حجم الشاشة
    final isSmallScreen = screenWidth < 350;
    final titleFontSize = isSmallScreen ? 24.0 : 28.0;
    final descFontSize = isSmallScreen ? 12.0 : 14.0;
    final buttonHeight = isSmallScreen ? 44.0 : 48.0;
    final horizontalPadding = isSmallScreen ? 16.0 : 24.0;
    final verticalPadding = isSmallScreen ? 16.0 : 20.0;
    
    return Container(
      margin: padding(horizontal: 20, vertical: 10),
      // إزالة الارتفاع الثابت لتجنب مشكلة الفيض
      // height: 220, // تمت إزالة هذا السطر
      decoration: BoxDecoration(
        borderRadius: borderRadius(radius: 20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF754FFE),
            Color(0xFF754FFE).withOpacity(0.8),
            Color(0xFF754FFE).withOpacity(0.9),
          ],
        ),
        boxShadow: [
          boxShadow(Color(0xFF754FFE).withOpacity(0.3), blur: 15, y: 8),
        ],
      ),
      child: Stack(
        children: [
          // خلفية الأنماط الزخرفية - تحسين التوزيع
          Positioned(
            right: -50,
            top: -30,
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.08),
              ),
            ),
          ),
          Positioned(
            left: -30,
            bottom: -40,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.06),
              ),
            ),
          ),
          // إضافة عنصر زخرفي إضافي للتوازن
          Positioned(
            left: 60,
            top: -20,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.04),
              ),
            ),
          ),
          
          // محتوى الصورة الرئيسية
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding, 
              vertical: verticalPadding
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min, // مهم لتجنب مشاكل التخطيط
              children: [
                Text(
                  'اكتشف عالم التعلم',
                  style: style24Bold().copyWith(
                    color: Colors.white,
                    fontSize: titleFontSize,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: isSmallScreen ? 6 : 8),
                Text(
                  'ارتقِ بمستواك التعليمي مع أفضل الدورات المتقدمة التي تجمع بين البث المباشر، والمتابعة المستمرة، والاختبارات المكثفة، في تجربة تعليمية تضمن لك التميز والنجاح',
                  style: style14Regular().copyWith(
                    color: Colors.white.withOpacity(0.9),
                    height: 1.5,
                    fontSize: descFontSize,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 4, // تحديد عدد الأسطر لتجنب الفيض
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: isSmallScreen ? 16 : 20),
                // الأزرار بتنسيق متناسق وأنيق - تحسين الاستجابة
                LayoutBuilder(
                  builder: (context, constraints) {
                    // في الشاشات الصغيرة جداً، نضع الأزرار عمودياً
                    if (constraints.maxWidth < 280) {
                      return Column(
                        children: [
                          // زر تطبيق مفتاح شراء
                          SizedBox(
                            width: double.infinity,
                            height: buttonHeight,
                            child: button(
                              onTap: () {
                                locator<PageProvider>().setPage(PageNames.keyApp);
                              },
                              width: double.infinity,
                              height: buttonHeight,
                              text: 'تطبيق مفتاح شراء',
                              bgColor: Colors.white,
                              textColor: Color(0xFF754FFE),
                              raduis: 16,
                            ),
                          ),
                          SizedBox(height: 8),
                          // زر الفيديو
                          GestureDetector(
                            onTap: () {
                              _playIntroVideo();
                            },
                            child: Container(
                              width: buttonHeight,
                              height: buttonHeight,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.25),
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white.withOpacity(0.4), width: 2),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.white.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.play_arrow,
                                color: Colors.white,
                                size: isSmallScreen ? 20 : 24,
                              ),
                            ),
                          ),
                        ],
                      );
                    } else {
                      // التخطيط الأفقي للشاشات العادية
                      return Row(
                        children: [
                          // زر استكشاف الدورات (يمين) - يأخذ مساحة أكبر
                          Expanded(
                            flex: 3,
                            child: SizedBox(
                              height: buttonHeight,
                              child: button(
                                onTap: () {
                                  locator<PageProvider>().setPage(PageNames.keyApp);
                                },
                                width: double.infinity,
                                height: buttonHeight,
                                text: 'تطبيق مفتاح شراء',
                                bgColor: Colors.white,
                                textColor: Color(0xFF754FFE),
                                raduis: 16,
                              ),
                            ),
                          ),
                          SizedBox(width: isSmallScreen ? 10 : 15),
                          // زر التشغيل الدائري (يسار) - حجم ثابت وأنيق
                          GestureDetector(
                            onTap: () {
                              _playIntroVideo();
                            },
                            child: Container(
                              width: buttonHeight,
                              height: buttonHeight,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.25),
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white.withOpacity(0.4), width: 2),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.white.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.play_arrow,
                                color: Colors.white,
                                size: isSmallScreen ? 20 : 24,
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                  },
                ),
                // إضافة مساحة إضافية في الأسفل
                SizedBox(height: isSmallScreen ? 8 : 12),
              ],
            ),
          ),
          
          // تم إزالة الأيقونة التعليمية لجعل التصميم أكثر نظافة وأناقة
        ],
      ),
    );
  }

  // Widget لقسم خصم يصل إلى 70% - تصميم أنيق ومتقدم
  Widget _buildEnhancedDiscountSection() {
    return Container(
      margin: padding(horizontal: 20, vertical: 10),
      height: 120,
      decoration: BoxDecoration(
        borderRadius: borderRadius(radius: 20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF6B6B),
            Color(0xFFFF8E53),
            Color(0xFFFF6B9D),
          ],
        ),
        boxShadow: [
          boxShadow(Color(0xFFFF6B6B).withOpacity(0.4), blur: 20, y: 10),
        ],
      ),
      child: Stack(
        children: [
          // خلفية الأنماط الزخرفية
          Positioned(
            right: -30,
            top: -20,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.1),
              ),
            ),
          ),
          Positioned(
            left: -40,
            bottom: -30,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.08),
              ),
            ),
          ),
          
          // محتوى القسم
          Padding(
            padding: padding(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                // النص
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          Text(
                            'خصم ',
                            style: style16Bold().copyWith(color: Colors.white),
                          ),
                          isLoadingDiscountPercent 
                            ? Text(
                                '...',
                                style: style20Bold().copyWith(
                                  color: Colors.white,
                                  fontSize: 24,
                                ),
                              )
                            : Text(
                                '$generalDiscountPercent%',
                                style: style20Bold().copyWith(
                                  color: Colors.white,
                                  fontSize: 24,
                                ),
                              ),
                        ],
                      ),
                      SizedBox(height: 4),
                      Text(
                        'على جميع الدورات المميزة',
                        style: style12Regular().copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // أيقونة بسيطة
                Container(
                  padding: padding(all: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.percent,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Widget للفئات المميزة مع تصميم محسن
  Widget _buildEnhancedFeaturedSection() {
    return Column(
                                    children: [
        space(20),
        Container(
          margin: padding(horizontal: 20),
          child: Row(
            children: [
              Container(
                padding: padding(all: 8),
                decoration: BoxDecoration(
                  color: Color(0xFF754FFE).withOpacity(0.1),
                  borderRadius: borderRadius(radius: 10),
                ),
                child: SvgPicture.asset(
                  AppAssets.starYellowSvg,
                  width: 20,
                  height: 20,
                  color: Color(0xFF754FFE),
                ),
              ),
              space(12),
              Text(
                appText.featuredClasses,
                style: style20Bold().copyWith(color: grey33),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  // الانتقال إلى صفحة الدورات
                  nextRoute('/course-overview');
                },
                child: Container(
                  padding: padding(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Color(0xFF754FFE).withOpacity(0.1),
                    borderRadius: borderRadius(radius: 8),
                  ),
                  child: Text(
                    'عرض الكل',
                    style: style12Bold().copyWith(color: Color(0xFF754FFE)),
                  ),
                ),
              ),
            ],
          ),
        ),
        space(16),
                
                                      if(featuredListData.isNotEmpty || isLoadingFeaturedListData)...{
                                        SizedBox(
                                          width: getSize().width,
            height: 240,
                                          child: PageView(
                                            controller: sliderPageController,
                                            onPageChanged: (value) async {
                                              await Future.delayed(const Duration(milliseconds: 500));
                                              setState(() {
                                                currentSliderIndex = value;
                                              });
                                            },
                                            physics: const BouncingScrollPhysics(),
              children: List.generate( 
                isLoadingFeaturedListData ? 1 : featuredListData.length, 
                (index) {
                                              return isLoadingFeaturedListData
                                              ? courseSliderItemShimmer()
                    : _buildEnhancedCourseCard(featuredListData[index]);
                }
              ),
            ),
          ),
          
          space(15),
          
          // مؤشر الصفحات محسن
                                        SizedBox(
                                          width: getSize().width,
                                          height: 15,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              ...List.generate(featuredListData.length, (index) {
                                                return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: currentSliderIndex == index ? 24 : 8,
                    height: 8,
                    margin: padding(horizontal: 4),
                                                  decoration: BoxDecoration(
                      color: currentSliderIndex == index ? Color(0xFF754FFE) : greyE7,
                      borderRadius: borderRadius(radius: 4),
                                                  ),
                                                );
                                              }),
                                            ],
                                          ),
          ),
        },
      ],
    );
  }

  // تصميم محسن لبطاقة الدورة مع وظيفة النقر
  Widget _buildEnhancedCourseCard(CourseModel course) {
    return GestureDetector(
      onTap: () {
        // الانتقال إلى صفحة الدورة
        nextRoute(
          '/single-course', 
          arguments: [course.id, course.type == 'bundle']
        );
      },
      child: Container(
        margin: padding(horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius(radius: 20),
          boxShadow: [
            boxShadow(Colors.black.withOpacity(0.1), blur: 20, y: 8),
          ],
        ),
        child: Stack(
          children: [
            // الصورة الرئيسية
            ClipRRect(
              borderRadius: borderRadius(radius: 20),
              child: Container(
                height: 140,
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF754FFE).withOpacity(0.8), Color(0xFF754FFE).withOpacity(0.6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: course.image != null 
                  ? Image.network(
                      course.image!,
                      fit: BoxFit.cover,
                    )
                  : Center(
                      child: SvgPicture.asset(
                        AppAssets.videoLineSvg,
                        color: Colors.white,
                        width: 50,
                        height: 50,
                      ),
                    ),
              ),
            ),
            
            // شارة الخصم
            if(course.discountPercent != null && course.discountPercent! > 0)
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: padding(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: red49,
                    borderRadius: borderRadius(radius: 8),
                  ),
                  child: Text(
                    '${course.discountPercent}% خصم',
                    style: style10Bold().copyWith(color: Colors.white),
                  ),
                ),
              ),
            
            // معلومات الدورة
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: padding(all: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      course.title ?? '',
                      style: style14Bold().copyWith(color: grey33),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    space(8),
                    Row(
                      children: [
                        Icon(Icons.star, color: yellow29, size: 16),
                        space(4),
                        Text(
                          course.rate?.toString() ?? '0',
                          style: style12Regular().copyWith(color: greyB2),
                        ),
                        const Spacer(),
                        if(course.price != null)
                          Text(
                            '${course.price} \$',
                            style: style14Bold().copyWith(color: Color(0xFF754FFE)),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget للتقييمات والمراجعات
  Widget _buildStudentReviews() {
    final reviews = [
      {
        'name': 'سارة أحمد',
        'course': 'دورة تطوير التطبيقات',
        'rating': '5',
        'review': 'دورة ممتازة ومفيدة جداً، المحتوى واضح والشرح مبسط',
      },
      {
        'name': 'محمد علي',
        'course': 'دورة التصميم الجرافيكي',
        'rating': '5',
        'review': 'أفضل دورة تصميم أخذتها، تعلمت الكثير من المهارات الجديدة',
      },
    ];

    return Container(
      margin: padding(horizontal: 20, vertical: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
          Row(
            children: [
              Container(
                padding: padding(all: 8),
                decoration: BoxDecoration(
                  color: yellow29.withOpacity(0.1),
                  borderRadius: borderRadius(radius: 10),
                ),
                child: SvgPicture.asset(
                  AppAssets.starYellowSvg,
                  width: 20,
                  height: 20,
                  color: yellow29,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'آراء الطلاب',
                  style: style18Bold().copyWith(color: grey33),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                'عرض الكل',
                style: style12Bold().copyWith(color: Color(0xFF754FFE)),
              ),
            ],
          ),
          SizedBox(height: 18),
                                        SizedBox(
            height: 140,
            child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: reviews.length,
              itemBuilder: (context, index) {
                final review = reviews[index];
                return Container(
                  width: 260,
                  margin: EdgeInsets.only(right: 12),
                  padding: padding(all: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: borderRadius(radius: 12),
                    boxShadow: [
                      boxShadow(Colors.black.withOpacity(0.06), blur: 8, y: 3),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 35,
                            height: 35,
                            decoration: BoxDecoration(
                              color: Color(0xFF754FFE).withOpacity(0.1),
                              borderRadius: borderRadius(radius: 18),
                            ),
                            child: Center(
                              child: Text(
                                review['name']!.substring(0, 1),
                                style: style12Bold().copyWith(color: Color(0xFF754FFE)),
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  review['name']!,
                                  style: style12Bold().copyWith(color: grey33),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 2),
                                Text(
                                  review['course']!,
                                  style: style9Regular().copyWith(color: greyB2),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: List.generate(5, (starIndex) {
                              return Icon(
                                Icons.star,
                                color: starIndex < int.parse(review['rating']!) 
                                  ? yellow29 
                                  : greyE7,
                                size: 12,
                                                    );
                                              }),
                                            ),
                        ],
                      ),
                      SizedBox(height: 12),
                      Expanded(
                        child: Text(
                          review['review']!,
                          style: style11Regular().copyWith(
                            color: grey33,
                            height: 1.3,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // قسم بانر المدرس الرئيسي الجديد والجذاب
  Widget _buildMainTeacherBanner() {
    return Container(
      margin: padding(horizontal: 20, vertical: 10),
      height: 160,
                                    decoration: BoxDecoration(
        borderRadius: borderRadius(radius: 20),
        color: greyFA,
        boxShadow: [
          boxShadow(Colors.black.withOpacity(0.06), blur: 12, y: 4),
        ],
      ),
                                    child: Row(
                                      children: [
          // النص على اليسار
          Expanded(
            flex: 7,
            child: Padding(
              padding: padding(horizontal: 16, vertical: 16),
              child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                  // شعار "منصة السلطان"
                  Container(
                    padding: padding(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(0xFF754FFE).withOpacity(0.1),
                      borderRadius: borderRadius(radius: 16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: padding(all: 3),
                          decoration: BoxDecoration(
                            color: Color(0xFF754FFE),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.waving_hand,
                            color: Colors.white,
                            size: 10,
                          ),
                        ),
                        SizedBox(width: 6),
                        Flexible(
                          child: Text(
                            'مرحباً بك في منصة السلطان',
                            style: style12Bold().copyWith(color: Color(0xFF754FFE)),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 8),
                                            Text(
                    'أ. سلام طالب السلطان',
                    style: style18Bold().copyWith(
                      color: grey33,
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 6),
                  Container(
                    padding: padding(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: borderRadius(radius: 10),
                      boxShadow: [
                        boxShadow(Colors.black.withOpacity(0.04), blur: 6, y: 2),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.school,
                          color: Color(0xFF754FFE),
                          size: 12,
                        ),
                        SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            'مدرس قواعد اللغة العربية',
                            style: style10Regular().copyWith(color: grey33),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // صورة المدرس على اليمين مع تأثيرات متقدمة
          SizedBox(
            width: 100,
            height: 160,
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
                                    children: [
                // صورة المدرس الأساسية
                Positioned(
                  right: 8,
                  child: Container(
                    width: 80,
                    height: 120,
                    decoration: BoxDecoration(
                      borderRadius: borderRadius(radius: 16),
                      boxShadow: [
                        boxShadow(Colors.black.withOpacity(0.1), blur: 8, y: 4),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: borderRadius(radius: 16),
                      child: Image.asset(
                        'assets/image/png/1.png',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),

                // تأثير الإطار المضيء
                Positioned(
                  right: 6,
                  child: Container(
                    width: 84,
                    height: 124,
                    decoration: BoxDecoration(
                      borderRadius: borderRadius(radius: 18),
                      border: Border.all(
                        color: Color(0xFF754FFE).withOpacity(0.2),
                        width: 2,
                      ),
                    ),
                  ),
                ),

                // شارة "موثوق"
                Positioned(
                  top: 8,
                  right: -2,
                  child: Container(
                    padding: padding(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                      color: Color(0xFF754FFE),
                      borderRadius: borderRadius(radius: 10),
                      boxShadow: [
                        boxShadow(Color(0xFF754FFE).withOpacity(0.3), blur: 6, y: 2),
                      ],
                    ),
                                            child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.verified,
                          color: Colors.white,
                          size: 10,
                        ),
                        SizedBox(width: 3),
                        Text(
                          'موثوق',
                          style: style8Bold().copyWith(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // قسم الدورات المتاحة حالياً - تصميم أنيق ومتقدم
  Widget _buildAvailableCoursesSection() {
    return Container(
      margin: padding(horizontal: 20, vertical: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم مع أيقونة
          Row(
            children: [
              Container(
                padding: padding(all: 8),
                decoration: BoxDecoration(
                  color: Color(0xFF754FFE).withOpacity(0.1),
                  borderRadius: borderRadius(radius: 10),
                ),
                child: Icon(
                  Icons.school_outlined,
                  color: Color(0xFF754FFE),
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'الدورات المتاحة حالياً',
                  style: style18Bold().copyWith(color: grey33),
                ),
              ),
              // شارة "جديد"
              Container(
                padding: padding(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(0xFF754FFE),
                  borderRadius: borderRadius(radius: 12),
                ),
                child: Text(
                  'جديد',
                  style: style10Bold().copyWith(color: Colors.white),
                ),
              ),
              SizedBox(width: 8),
              GestureDetector(
                onTap: () {
                  // الانتقال إلى صفحة جميع الدورات
                  locator<PageProvider>().setPage(PageNames.categories);
                },
                child: Text(
                  'عرض الكل',
                  style: style12Bold().copyWith(color: Color(0xFF754FFE)),
                ),
              ),
            ],
          ),
          SizedBox(height: 18),
          
          // عرض الدورات
          if (isLoadingAvailableCoursesData)
            // شيمر التحميل
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 3,
                itemBuilder: (context, index) {
                  return Container(
                    width: 280,
                    margin: EdgeInsets.only(right: 12),
                    child: courseSliderItemShimmer(),
                  );
                },
              ),
            )
          else if (availableCoursesData.isEmpty)
            // حالة عدم وجود دورات
            Container(
              height: 140,
              width: double.infinity,
              decoration: BoxDecoration(
                color: greyF8,
                borderRadius: borderRadius(radius: 16),
                border: Border.all(color: greyE7, width: 1),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.school_outlined,
                    color: greyB2,
                    size: 40,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'لا توجد دورات متاحة حالياً',
                    style: style14Regular().copyWith(color: greyB2),
                  ),
                ],
              ),
            )
          else
            // عرض الدورات الفعلية
            SizedBox(
              height: 280,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                itemCount: availableCoursesData.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 240,
                    margin: EdgeInsets.only(right: 12),
                    child: _buildAvailableCourseCard(availableCoursesData[index]),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  // بطاقة دورة متاحة بتصميم أنيق
  Widget _buildAvailableCourseCard(CourseModel course) {
    return GestureDetector(
      onTap: () {
        // الانتقال إلى صفحة الدورة
        nextRoute(
          '/single-course', 
          arguments: [course.id, course.type == 'bundle']
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius(radius: 16),
          boxShadow: [
            boxShadow(Colors.black.withOpacity(0.08), blur: 12, y: 4),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الدورة
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: Container(
                    height: 140,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF754FFE).withOpacity(0.8), 
                          Color(0xFF754FFE).withOpacity(0.6)
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: course.image != null 
                      ? Image.network(
                          course.image!,
                          fit: BoxFit.cover,
                        )
                      : Center(
                          child: Icon(
                            Icons.play_circle_outline,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                  ),
                ),
                
                // شارة الخصم
                if (course.discountPercent != null && course.discountPercent! > 0)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: padding(horizontal: 6, vertical: 3),
                      decoration: BoxDecoration(
                        color: red49,
                        borderRadius: borderRadius(radius: 8),
                      ),
                      child: Text(
                        '${course.discountPercent}% خصم',
                        style: style9Bold().copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                
                // شارة "متاح الآن"
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: padding(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                      color: green77(),
                      borderRadius: borderRadius(radius: 8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          'متاح',
                          style: style9Bold().copyWith(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            
            // معلومات الدورة
            Expanded(
              child: Padding(
                padding: padding(all: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الدورة
                    Text(
                      course.title ?? '',
                      style: style13Bold().copyWith(color: grey33),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 6),
                    
                    // معلومات المدرس
                    if (course.teacher != null)
                      Row(
                        children: [
                          Icon(
                            Icons.person_outline,
                            color: greyB2,
                            size: 14,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              course.teacher!.fullName ?? '',
                              style: style10Regular().copyWith(color: greyB2),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    SizedBox(height: 4),
                    
                    // التقييم وعدد الطلاب
                    Row(
                      children: [
                        Icon(Icons.star, color: yellow29, size: 14),
                        SizedBox(width: 3),
                        Text(
                          course.rate?.toString() ?? '0',
                          style: style10Regular().copyWith(color: greyB2),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.people_outline, color: greyB2, size: 14),
                        SizedBox(width: 3),
                        Text(
                          '${course.studentsCount ?? 0}',
                          style: style10Regular().copyWith(color: greyB2),
                        ),
                      ],
                    ),
                    
                    Spacer(),
                    
                    // السعر والزر
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (course.price != null)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (course.discountPercent != null && course.discountPercent! > 0)
                                Text(
                                  '${course.price} \$',
                                  style: style10Regular().copyWith(
                                    color: greyB2,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                              Text(
                                '${course.priceWithDiscount ?? course.price} \$',
                                style: style13Bold().copyWith(color: Color(0xFF754FFE)),
                              ),
                            ],
                          ),
                        Container(
                          padding: padding(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Color(0xFF754FFE).withOpacity(0.1),
                            borderRadius: borderRadius(radius: 6),
                          ),
                          child: Text(
                            'التفاصيل',
                            style: style10Bold().copyWith(color: Color(0xFF754FFE)),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // قسم الدعم الفني الجديد - تصميم جذاب ومتقدم
  Widget _buildTechnicalSupportSection() {
    return Container(
      margin: padding(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius(radius: 20),
        boxShadow: [
          boxShadow(Colors.black.withOpacity(0.08), blur: 20, y: 8),
        ],
      ),
      child: Column(
        children: [
          // عنوان القسم مع أيقونة
          Container(
            padding: padding(all: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF754FFE).withOpacity(0.1),
                  Color(0xFF754FFE).withOpacity(0.05),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                // أيقونة الدعم
                Container(
                  padding: padding(all: 12),
                  decoration: BoxDecoration(
                    color: Color(0xFF754FFE),
                    borderRadius: borderRadius(radius: 14),
                    boxShadow: [
                      boxShadow(Color(0xFF754FFE).withOpacity(0.3), blur: 12, y: 4),
                    ],
                  ),
                  child: Icon(
                    Icons.support_agent,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الدعم الفني',
                        style: style18Bold().copyWith(color: grey33),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'نحن هنا لمساعدتك في أي وقت',
                        style: style12Regular().copyWith(color: greyB2),
                      ),
                    ],
                  ),
                ),
                // شارة متاح 24/7
                Container(
                  padding: padding(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: green77(),
                    borderRadius: borderRadius(radius: 12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 4),
                      Text(
                        'متاح الآن',
                        style: style10Bold().copyWith(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // أزرار خيارات الدعم
          Padding(
            padding: padding(horizontal: 20, vertical: 20),
            child: Column(
              children: [
                // الصف الأول - التذاكر ودعم الفصول
                Row(
                  children: [
                    // زر التذاكر
                    Expanded(
                      child: _buildSupportOptionCard(
                        icon: Icons.confirmation_number_outlined,
                        title: 'التذاكر',
                        description: 'إنشاء تذكرة دعم جديدة',
                        color: Color(0xFF754FFE),
                        onTap: () {
                          // فتح صفحة الدعم مع تبويب التذاكر (0)
                          nextRoute(SupportMessagePage.pageName, arguments: {'initialTab': 0});
                        },
                      ),
                    ),
                    SizedBox(width: 12),
                    // زر دعم الفصول
                    Expanded(
                      child: _buildSupportOptionCard(
                        icon: Icons.class_outlined,
                        title: 'دعم الفصول',
                        description: 'المساعدة في الدروس',
                        color: Color(0xFF754FFE),
                        onTap: () {
                          // فتح صفحة الدعم مع تبويب دعم الفصول (1)
                          nextRoute(SupportMessagePage.pageName, arguments: {'initialTab': 1});
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                
                // الصف الثاني - الدردشة المباشرة
                _buildSupportOptionCard(
                  icon: Icons.chat_bubble_outline,
                  title: 'الدردشة المباشرة',
                  description: 'تحدث مع فريق الدعم فوراً',
                  color: green77(),
                  isFullWidth: true,
                  onTap: () {
                    // فتح صفحة الدعم مع تبويب الدردشة المباشرة (2)
                    nextRoute(SupportMessagePage.pageName, arguments: {'initialTab': 2});
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بطاقة خيار دعم
  Widget _buildSupportOptionCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
    bool isFullWidth = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding(all: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.05),
          borderRadius: borderRadius(radius: 12),
          border: Border.all(
            color: color.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: isFullWidth 
          ? Row(
              children: [
                Container(
                  padding: padding(all: 8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: borderRadius(radius: 10),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: style14Bold().copyWith(color: grey33),
                      ),
                      SizedBox(height: 2),
                      Text(
                        description,
                        style: style10Regular().copyWith(color: greyB2),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 16,
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: padding(all: 8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: borderRadius(radius: 10),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                SizedBox(height: 12),
                Text(
                  title,
                  style: style12Bold().copyWith(color: grey33),
                ),
                SizedBox(height: 4),
                Text(
                  description,
                  style: style9Regular().copyWith(color: greyB2),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppLanguageProvider>(
      builder: (context, languageProvider, _) {
        return directionality(
          child: Consumer<DrawerProvider>(
            builder: (context, drawerProvider, _) {
              return ClipRRect(
                borderRadius: borderRadius(radius: drawerProvider.isOpenDrawer ? 20 : 0),
                child: Scaffold(
                  backgroundColor: greyFA,
                  body: Column(
                                    children: [
                      // app bar
                      HomeWidget.homeAppBar(appBarController, appBarAnimation, token, searchController, searchNode, name),
                      
                      // body
                      Expanded(
                                        child: SingleChildScrollView(
                          controller: scrollController,
                                          physics: const BouncingScrollPhysics(),
                          child: Column(
                            children: [
                              // بانر المدرس الرئيسي
                              _buildMainTeacherBanner(),
                              space(15),
                              
                              // صورة رئيسية جذابة
                              _buildHeroBanner(),
                              space(15),
                              
                              // قسم خصم يصل إلى 70%
                              _buildEnhancedDiscountSection(),
                              space(15),
                              
                              // عرض الدورات المتاحة حالياً
                              _buildAvailableCoursesSection(),
                              space(15),
                              
                              // قسم الدعم الفني الجديد
                              _buildTechnicalSupportSection(),
                              space(15),
                              
                              // باقي الأقسام...
                              space(100),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

